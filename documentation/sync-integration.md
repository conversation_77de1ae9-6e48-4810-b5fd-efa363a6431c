# Supabase Sync-Integration - FoxFlow Budget Manager Pro

## Übersicht

Die FoxFlow App verfügt über eine vollständige Supabase-Integration für die Synchronisation zwischen mehreren Geräten. Das System kombiniert LocalStorage für schnelle lokale Zugriffe mit Supabase für Cloud-Synchronisation.

## Architektur

### Hybrid Storage System

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   LocalStorage  │◄──►│  Hybrid Adapter │◄──►│    Supabase     │
│   (Primary)     │    │  (Sync Engine)  │    │   (Cloud DB)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
        ▲                        ▲                        ▲
        │                        │                        │
   Fast Access              Intelligent              Multi-Device
   Offline Ready            Routing                  Sync
```

### Komponenten

1. **LocalStorageAdapter** - Lokaler Speicher für schnelle Zugriffe
2. **SupabaseAdapter** - Direkte Supabase-Kommunikation
3. **HybridStorageAdapter** - Intelligente Kombination beider
4. **SyncService** - Automatische Synchronisation
5. **SyncProvider** - React Context für UI-Integration

## Konfiguration

### Umgebungsvariablen (.env.local)

```env
NEXT_PUBLIC_SUPABASE_URL=https://epqhwpalixjcjbxaonkb.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJh...
NEXT_PUBLIC_SYNC_ENABLED=true
NEXT_PUBLIC_SYNC_INTERVAL=30000
```

### Supabase-Datenbankschema

**Haupttabellen:**
- `categories` - Kategorien mit Typen und Farben
- `transactions` - Transaktionen mit Kategoriezuordnung
- `savings_goals` - Sparziele mit Fortschritt
- `investments` - Investitionen mit Wertentwicklung
- `account_balances` - Kontostände nach Monat/Jahr
- `settings` - Benutzereinstellungen
- `sync_metadata` - Sync-Tracking für Konfliktauflösung

**Zusätzliche Features:**
- Row Level Security (RLS) aktiviert
- Automatische Timestamps (created_at, updated_at)
- Device-Tracking für Multi-Device-Sync
- Indizes für Performance-Optimierung

## Sync-Strategien

### Automatischer Sync

- **App-Start:** Initial-Sync beim Laden der App
- **Netzwerk-Reconnect:** Sync bei Wiederherstellung der Internetverbindung
- **Intervall-Sync:** Regelmäßiger Sync alle 30 Sekunden (konfigurierbar)
- **Focus-Event:** Sync bei Fokus-Wiederherstellung der App

### Manueller Sync

- **Bidirektional:** Sync in beide Richtungen (Standard)
- **Push:** Nur lokale Daten zu Supabase
- **Pull:** Nur Supabase-Daten zu lokal
- **Force-Sync:** Überschreibt lokale Daten mit Remote-Daten

### Konfliktauflösung

- **Last-Write-Wins:** Neueste Änderung gewinnt basierend auf `updated_at`
- **Device-Tracking:** Jeder Datensatz hat eine `device_id`
- **Checksum-Validierung:** Integrität der Daten wird überprüft

## UI-Integration

### Dashboard

- **Kompakter Sync-Status** in der Header-Leiste
- **Echtzeit-Updates** des Online/Offline-Status
- **Visuelle Indikatoren** für Sync-Fortschritt

### Settings-Seite

- **Detaillierter Sync-Status** mit Statistiken
- **Manuelle Sync-Buttons** für verschiedene Modi
- **Verbindungstest** und Geräteinformationen
- **Sync-Historie** und letzte Sync-Zeit

### Sync-Status-Komponente

```tsx
// Kompakte Ansicht
<SyncStatus compact />

// Detaillierte Ansicht
<SyncStatus showDetails />
```

## Verwendung

### Automatische Integration

Das Sync-System ist automatisch aktiv, wenn `NEXT_PUBLIC_SYNC_ENABLED=true` gesetzt ist:

```tsx
// Automatisch verfügbar über SyncProvider
const syncContext = useSyncContext();

// Manueller Sync
await syncContext.performSync();

// Status abfragen
const { isOnline, lastSync, deviceId } = syncContext;
```

### Storage-Adapter

```tsx
// Automatische Auswahl basierend auf Konfiguration
const useSupabaseSync = process.env.NEXT_PUBLIC_SYNC_ENABLED === 'true';
const storageAdapter = useSupabaseSync ? hybridStorageAdapter : localStorageAdapter;

// Repository verwenden
const categoryRepository = new Repository<Category>(storageAdapter, 'categories');
```

## Offline-Fähigkeit

- **Vollständig offline-fähig:** App funktioniert ohne Internet
- **Automatischer Fallback:** Bei Netzwerkproblemen auf LocalStorage
- **Queue-System:** Änderungen werden bei Reconnect synchronisiert
- **Transparente Umschaltung:** Benutzer merkt keinen Unterschied

## Performance

- **Lazy Loading:** Daten werden nur bei Bedarf von Supabase geladen
- **Background Sync:** Synchronisation läuft im Hintergrund
- **Caching:** LocalStorage als primärer Cache
- **Delta Sync:** Nur geänderte Daten werden übertragen

## Sicherheit

- **Row Level Security:** Aktiviert auf allen Tabellen
- **Device-Isolation:** Jedes Gerät hat eine eindeutige ID
- **Anon Key:** Nur anonymer Zugriff, keine Authentifizierung erforderlich
- **HTTPS:** Alle Verbindungen verschlüsselt

## Monitoring

### Logs

```javascript
// Sync-Status verfolgen
console.log('Storage-Modus: Hybrid (LocalStorage + Supabase)');
console.log('Sync-Service erfolgreich gestartet');
console.log('categories: 5 Einträge zu Supabase gepusht');
```

### Metriken

- Sync-Erfolgsrate
- Übertragene Datensätze
- Sync-Dauer
- Verbindungsstatus

## Troubleshooting

### Häufige Probleme

1. **Sync funktioniert nicht:**
   - Internetverbindung prüfen
   - Supabase-URL und Key validieren
   - Browser-Konsole auf Fehler prüfen

2. **Daten nicht synchron:**
   - Manuellen Sync durchführen
   - Device-ID überprüfen
   - Timestamps vergleichen

3. **Performance-Probleme:**
   - Sync-Intervall erhöhen
   - Datenbank-Indizes prüfen
   - Netzwerklatenz messen

### Debug-Befehle

```javascript
// Verbindung testen
await syncService.testConnection();

// Sync-Status abrufen
const status = syncService.getStatus();

// Vollständiger Sync erzwingen
await syncService.forceFullSync();
```

## Zukunft

### Geplante Features

- **Real-time Sync:** WebSocket-basierte Live-Updates
- **Selective Sync:** Nur bestimmte Datentypen synchronisieren
- **Backup/Restore:** Vollständige Datensicherung
- **Multi-User:** Unterstützung für mehrere Benutzer
- **Encryption:** Ende-zu-Ende-Verschlüsselung

### Migration

Das System ist so konzipiert, dass es einfach erweitert werden kann:

- Neue Storage-Adapter können hinzugefügt werden
- Sync-Strategien sind austauschbar
- UI-Komponenten sind wiederverwendbar
- Konfiguration ist flexibel

## Fazit

Die Supabase-Integration bietet eine robuste, skalierbare Lösung für Multi-Device-Synchronisation mit optimaler Benutzererfahrung durch Offline-Fähigkeit und automatische Konfliktauflösung.
