# BudgetPlannerAdv - Projektplanung

Dieses Dokument beschreibt die Planung und Architektur des BudgetPlannerAdv-Projekts, einer 10-Jahres-Budgetplanungsanwendung, die lokal auf Mac-Computern läuft.

## Projektstruktur

Die Projektstruktur ist klar definiert und sollte wie folgt beibehalten werden:

```
BudgetManagerPro/                  # Hauptverzeichnis des Projekts
├── budget-planner-adv/            # Anwendungsverzeichnis
│   ├── src/                       # Next.js Frontend-Code
│   │   ├── app/                   # Next.js App Router Seiten
│   │   ├── components/            # React-Komponenten
│   │   ├── db/                    # Datenbankschnittstelle
│   │   └── lib/                   # Hilfsfunktionen und Utilities
│   ├── src-tauri/                 # Tauri Backend-Code
│   │   ├── src/                   # Rust-Code für Tauri
│   │   └── capabilities/          # Tauri-Berechtigungen
│   ├── public/                    # Statische Dateien
│   ├── package.json               # NPM-Konfiguration
│   └── ...                        # Weitere Konfigurationsdateien
└── documentation/                 # Projektdokumentation
    ├── design.md                  # Design-Dokumentation
    ├── planning.md                # Diese Datei
    ├── readme.md                  # Projektübersicht
    ├── researchstack.md           # Technische Recherche
    ├── shadcn-context.md          # ShadCN-Komponenten-Dokumentation
    └── task.md                    # Aufgabenliste
```

**Wichtig**: Es gibt keine verschachtelten Ordner mit dem gleichen Namen. Die Anwendung befindet sich im Ordner `budget-planner-adv` und enthält alle notwendigen Dateien.

## Architektur

Die Anwendung folgt einer klaren Architektur:

1. **Frontend (Next.js)**
   - App Router für Routing und Seitenstruktur
   - React-Komponenten für UI-Elemente
   - ShadCN UI für konsistentes Design
   - Tailwind CSS für Styling

2. **Backend (Tauri)**
   - Rust-basiertes Backend
   - SQLite-Datenbankintegration
   - Dateisystemzugriff
   - Systemintegrationen

3. **Datenbank (Hybrid Storage)**
   - **LocalStorage:** Primärer Cache für schnelle Zugriffe
   - **Supabase:** Cloud-Datenbank für Sync zwischen Geräten
   - **Automatischer Fallback:** Online → Supabase, Offline → LocalStorage
   - **Bidirektionale Synchronisation:** Push/Pull zwischen allen Geräten

4. **Sync-System (Supabase)**
   - **Device-spezifische Datentrennung**
   - **Automatischer Sync** bei App-Start und Netzwerk-Reconnect
   - **Konfliktauflösung** mit Last-Write-Wins-Strategie
   - **Real-time Status-Updates** und Benutzer-Feedback

## Datenbankschema

Das Datenbankschema umfasst die folgenden Haupttabellen:

1. **Transactions**
   - Einnahmen und Ausgaben
   - Kategorisierung
   - Datum und Betrag

2. **Categories**
   - Kategorien für Transaktionen
   - Typ (Einnahme, Ausgabe, Investment)
   - Farbe für Visualisierung

3. **SavingsGoals**
   - Sparziele mit Zielbeträgen
   - Aktueller Fortschritt
   - Zieldatum

4. **Investments**
   - Investitionen und deren Entwicklung
   - Anfangsbetrag und aktueller Wert
   - Kategorie und Startdatum

5. **AccountBalances**
   - Kontostände für private und geschäftliche Konten
   - Monat und Jahr
   - Aktueller Kontostand

6. **Settings**
   - Benutzereinstellungen
   - Darstellungsoptionen
   - Backup-Konfiguration

## Seitenstruktur

Die Anwendung umfasst die folgenden Hauptseiten:

1. **Dashboard** (`/`)
   - Übersicht über Einnahmen, Ausgaben und Budgets
   - Schnellzugriff auf wichtige Funktionen
   - Zusammenfassung des aktuellen Finanzstatus

2. **Transaktionen** (`/transactions`)
   - Liste aller Transaktionen
   - Filterung und Sortierung
   - Hinzufügen, Bearbeiten und Löschen von Transaktionen

3. **Kategorien** (`/categories`)
   - Verwaltung von Transaktionskategorien
   - Anpassung von Farben und Typen
   - Budgetierung nach Kategorien

4. **Investments** (`/investments`)
   - Tracking von Investitionen
   - Performance-Übersicht
   - Hinzufügen und Aktualisieren von Investments

5. **Sparziele** (`/savings`)
   - Verwaltung von Sparzielen
   - Fortschrittsanzeige
   - Einzahlungen und Abhebungen

6. **Diagramme** (`/charts`)
   - Visualisierung von Finanzdaten
   - Verschiedene Diagrammtypen
   - Anpassbare Zeiträume

7. **Berichte** (`/reports`)
   - Detaillierte Finanzberichte
   - Export-Funktionen
   - Anpassbare Berichtsparameter

8. **Vergleich** (`/compare`)
   - Vergleich von Finanzdaten zwischen verschiedenen Zeiträumen
   - Trendanalyse
   - Prognosen

9. **Einstellungen** (`/settings`)
   - Anpassung der Anwendung
   - Backup und Wiederherstellung
   - Benutzereinstellungen

## Komponenten

Die Anwendung verwendet eine modulare Komponentenstruktur:

1. **Layout-Komponenten**
   - Hauptlayout mit Sidebar
   - Responsive Design für verschiedene Bildschirmgrößen

2. **UI-Komponenten (ShadCN)**
   - Buttons, Cards, Forms, etc.
   - Konsistentes Design-System
   - Barrierefreiheit

3. **Funktionale Komponenten**
   - Transaktionsformulare
   - Diagramme und Visualisierungen
   - Filterung und Sortierung

4. **Datenbank-Komponenten**
   - Datenbankzugriff und -verwaltung
   - Caching und Optimierung
   - Fehlerbehandlung

## Entwicklungsplan

Die Entwicklung erfolgt in mehreren Phasen:

1. **Phase 1: Grundfunktionalität**
   - Implementierung des Layouts und der Navigation
   - Grundlegende Datenbankfunktionen
   - Dashboard und Transaktionsverwaltung

2. **Phase 2: Erweiterte Funktionen**
   - Kategorien und Budgetierung
   - Investments und Sparziele
   - Diagramme und Visualisierungen

3. **Phase 3: Berichte und Analysen**
   - Detaillierte Berichte
   - Vergleichsfunktionen
   - Export-Funktionen

4. **Phase 4: Desktop-Integration**
   - Tauri-Integration
   - SQLite-Datenbankanbindung
   - Dateisystemzugriff

5. **Phase 5: Optimierung und Polishing**
   - Performance-Optimierung
   - UI/UX-Verbesserungen
   - Fehlerbehandlung und Robustheit

## Technologiestack

Der Technologiestack umfasst:

- **Frontend**: Next.js 15.x, React 18.x
- **Styling**: Tailwind CSS 3.x, ShadCN UI
- **Backend**: Tauri 2.x, Rust
- **Datenbank**: SQLite 3.x
- **Build-Tools**: npm, Turbopack
- **Deployment**: Tauri-Bundler für macOS

## Wartung und Weiterentwicklung

Für die Wartung und Weiterentwicklung gelten folgende Richtlinien:

1. **Dokumentation**: Alle Änderungen müssen in den entsprechenden Dokumentationsdateien dokumentiert werden.
2. **Codequalität**: Der Code muss den Best Practices für React und Next.js folgen.
3. **Tests**: Neue Funktionen müssen getestet werden.
4. **Performance**: Die Anwendung muss auch mit großen Datenmengen performant bleiben.
5. **Sicherheit**: Sensible Daten müssen sicher gespeichert und verarbeitet werden.
