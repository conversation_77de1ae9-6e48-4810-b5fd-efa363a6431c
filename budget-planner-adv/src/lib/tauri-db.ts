// Tauri SQL Plugin Integration für BudgetPlannerAdv
// import { invoke } from '@tauri-apps/api/tauri'; // Nur für Tauri-Builds verfügbar
// Wir verwenden eine Mock-Implementierung für die Entwicklung
// Die tatsächliche Implementierung wird in der Tauri-App verwendet

// Mock-Typ für Database
interface Database {
  execute: (query: string, params?: any[]) => Promise<{
    rows: any[];
    lastInsertId: number;
    rowsAffected: number;
  }>;
}

// Singleton-Instanz der Datenbank
let dbInstance: Database | null = null;

// Initialisiere die Datenbankverbindung
export async function initializeDatabase(): Promise<Database> {
  if (!dbInstance) {
    try {
      // Mock-Implementierung für die Entwicklung
      dbInstance = {
        execute: async (query: string, params: any[] = []): Promise<any> => {
          console.log('Mock-Datenbankabfrage:', query, params);

          // Keine Dummy-Daten mehr, nur leere Ergebnisse zurückgeben
          if (query.includes('SELECT * FROM categories')) {
            return {
              rows: [],
              lastInsertId: 0,
              rowsAffected: 0
            };
          } else if (query.includes('SELECT * FROM transactions')) {
            return {
              rows: [],
              lastInsertId: 0,
              rowsAffected: 0
            };
          } else if (query.includes('INSERT INTO')) {
            return {
              rows: [],
              lastInsertId: Math.floor(Math.random() * 1000) + 10,
              rowsAffected: 1
            };
          } else if (query.includes('UPDATE')) {
            return {
              rows: [],
              lastInsertId: 0,
              rowsAffected: 1
            };
          } else if (query.includes('DELETE')) {
            return {
              rows: [],
              lastInsertId: 0,
              rowsAffected: 1
            };
          } else {
            return {
              rows: [],
              lastInsertId: 0,
              rowsAffected: 0
            };
          }
        }
      };
      console.log('Mock-Datenbankverbindung hergestellt');
    } catch (error) {
      console.error('Fehler beim Verbinden mit der Datenbank:', error);
      throw error;
    }
  }
  return dbInstance;
}

// Führe eine SQL-Abfrage aus
export async function executeQuery<T>(query: string, params: any[] = []): Promise<T[]> {
  try {
    const db = await initializeDatabase();
    const result = await db.execute(query, params);
    return result.rows as T[];
  } catch (error) {
    console.error('Fehler bei der Datenbankabfrage:', error);
    throw error;
  }
}

// Führe eine SQL-Abfrage aus, die einen einzelnen Wert zurückgibt
export async function executeScalar<T>(query: string, params: any[] = []): Promise<T | null> {
  try {
    const results = await executeQuery<T>(query, params);
    return results.length > 0 ? results[0] : null;
  } catch (error) {
    console.error('Fehler bei der Datenbankabfrage:', error);
    throw error;
  }
}

// Führe eine SQL-Abfrage aus, die eine Zeile einfügt und die ID zurückgibt
export async function executeInsert(query: string, params: any[] = []): Promise<number> {
  try {
    const db = await initializeDatabase();
    const result = await db.execute(query, params);
    return result.lastInsertId;
  } catch (error) {
    console.error('Fehler beim Einfügen in die Datenbank:', error);
    throw error;
  }
}

// Führe eine SQL-Abfrage aus, die Zeilen aktualisiert oder löscht
export async function executeUpdate(query: string, params: any[] = []): Promise<number> {
  try {
    const db = await initializeDatabase();
    const result = await db.execute(query, params);
    return result.rowsAffected;
  } catch (error) {
    console.error('Fehler beim Aktualisieren der Datenbank:', error);
    throw error;
  }
}

// Führe eine Transaktion aus
export async function executeTransaction(callback: (db: Database) => Promise<void>): Promise<void> {
  const db = await initializeDatabase();
  try {
    await db.execute('BEGIN TRANSACTION');
    await callback(db);
    await db.execute('COMMIT');
  } catch (error) {
    await db.execute('ROLLBACK');
    console.error('Fehler bei der Datenbanktransaktion:', error);
    throw error;
  }
}

// Prüfe, ob die Datenbank initialisiert ist
export async function isDatabaseInitialized(): Promise<boolean> {
  try {
    const db = await initializeDatabase();
    const result = await db.execute(
      "SELECT name FROM sqlite_master WHERE type='table' AND name='categories'"
    );
    return result.rows.length > 0;
  } catch (error) {
    console.error('Fehler beim Prüfen der Datenbankinitialisierung:', error);
    return false;
  }
}

// Erstelle die Datenbank-Tabellen, falls sie noch nicht existieren
export async function ensureDatabaseInitialized(): Promise<void> {
  const isInitialized = await isDatabaseInitialized();
  if (!isInitialized) {
    console.log('Datenbank wird initialisiert...');
    // Die Tabellen werden automatisch durch die Migrationen in lib.rs erstellt
  } else {
    console.log('Datenbank ist bereits initialisiert');
  }
}
