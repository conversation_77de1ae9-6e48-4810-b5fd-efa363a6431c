import { TransactionService } from './storage';
import type { Transaction } from './storage';

// Re-exportiere Transaction für andere Module
export type { Transaction };

// Typen für Transaktionen
export interface TransactionFilter {
  startDate?: string;
  endDate?: string;
  categoryId?: number;
  type?: string;
  minAmount?: number;
  maxAmount?: number;
  searchTerm?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  limit?: number;
  offset?: number;
}

export interface CategorySummary {
  categoryId: number;
  amount: number;
}

// CRUD-Operationen für Transaktionen
export async function getTransactions(filter: TransactionFilter = {}): Promise<Transaction[]> {
  // Hole alle Transaktionen
  const allTransactions = await TransactionService.getTransactions();

  // Filtere die Transaktionen basierend auf den Filterkriterien
  return allTransactions.filter(transaction => {
    // Startdatum
    if (filter.startDate && transaction.date < filter.startDate) {
      return false;
    }

    // Enddatum
    if (filter.endDate && transaction.date > filter.endDate) {
      return false;
    }

    // Kategorie
    if (filter.categoryId && transaction.categoryId !== filter.categoryId) {
      return false;
    }

    // Typ
    if (filter.type && transaction.type !== filter.type) {
      return false;
    }

    // Mindestbetrag
    if (filter.minAmount && transaction.amount < filter.minAmount) {
      return false;
    }

    // Höchstbetrag
    if (filter.maxAmount && transaction.amount > filter.maxAmount) {
      return false;
    }

    // Suchbegriff
    if (filter.searchTerm) {
      const searchTerm = filter.searchTerm.toLowerCase();
      const descriptionMatch = transaction.description.toLowerCase().includes(searchTerm);
      const notesMatch = transaction.notes ? transaction.notes.toLowerCase().includes(searchTerm) : false;

      if (!descriptionMatch && !notesMatch) {
        return false;
      }
    }

    return true;
  }).sort((a, b) => {
    // Sortierung
    if (filter.sortBy) {
      const sortOrder = filter.sortOrder === 'asc' ? 1 : -1;

      switch (filter.sortBy) {
        case 'date':
          return sortOrder * (new Date(a.date).getTime() - new Date(b.date).getTime());
        case 'amount':
          return sortOrder * (a.amount - b.amount);
        case 'description':
          return sortOrder * a.description.localeCompare(b.description);
        case 'category_id':
          return sortOrder * (a.categoryId - b.categoryId);
        default:
          return sortOrder * (new Date(b.date).getTime() - new Date(a.date).getTime());
      }
    }

    // Standardsortierung nach Datum absteigend
    return new Date(b.date).getTime() - new Date(a.date).getTime();
  }).slice(filter.offset || 0, filter.limit ? (filter.offset || 0) + filter.limit : undefined);
}

export async function getTransactionById(id: number): Promise<Transaction | null> {
  return await TransactionService.getTransactionById(id);
}

export async function createTransaction(transaction: Transaction): Promise<number> {
  return await TransactionService.createTransaction(transaction);
}

export async function updateTransaction(transaction: Transaction): Promise<void> {
  if (!transaction.id) throw new Error('Transaction ID is required');

  await TransactionService.updateTransaction(transaction);
}

export async function deleteTransaction(id: number): Promise<void> {
  await TransactionService.deleteTransaction(id);
}

// Hilfsfunktionen für Transaktionen
export async function getTransactionsByCategory(filter: TransactionFilter = {}): Promise<CategorySummary[]> {
  const transactions = await getTransactions(filter);

  // Gruppiere Transaktionen nach Kategorie
  const categoryMap = new Map<number, number>();

  for (const transaction of transactions) {
    if (transaction.type === 'expense' || transaction.type === 'business_expense' || transaction.type === 'investment') {
      const currentAmount = categoryMap.get(transaction.categoryId) || 0;
      categoryMap.set(transaction.categoryId, currentAmount + transaction.amount);
    }
  }

  // Erstelle das Ergebnis
  return Array.from(categoryMap.entries()).map(([categoryId, amount]) => ({
    categoryId,
    amount
  }));
}

export async function getMonthlyTransactionSummary(year: number, month: number): Promise<{
  income: number;
  expenses: number;
  savings: number;
}> {
  const startDate = new Date(year, month - 1, 1);
  const endDate = new Date(year, month, 0);

  const transactions = await getTransactions({
    startDate: startDate.toISOString().split('T')[0],
    endDate: endDate.toISOString().split('T')[0]
  });

  const income = transactions
    .filter(t => t.type === 'income')
    .reduce((sum, t) => sum + t.amount, 0);

  const expenses = transactions
    .filter(t => t.type === 'expense')
    .reduce((sum, t) => sum + t.amount, 0);

  return {
    income,
    expenses,
    savings: income - expenses
  };
}

export async function getYearlyTransactionSummary(year: number): Promise<{
  income: number;
  expenses: number;
  savings: number;
  monthlyData: {
    month: number;
    income: number;
    expenses: number;
    savings: number;
  }[];
}> {
  const startDate = new Date(year, 0, 1);
  const endDate = new Date(year, 11, 31);

  const transactions = await getTransactions({
    startDate: startDate.toISOString().split('T')[0],
    endDate: endDate.toISOString().split('T')[0]
  });

  const income = transactions
    .filter(t => t.type === 'income')
    .reduce((sum, t) => sum + t.amount, 0);

  const expenses = transactions
    .filter(t => t.type === 'expense')
    .reduce((sum, t) => sum + t.amount, 0);

  const monthlyData = [];
  for (let month = 1; month <= 12; month++) {
    const summary = await getMonthlyTransactionSummary(year, month);
    monthlyData.push({
      month,
      ...summary
    });
  }

  return {
    income,
    expenses,
    savings: income - expenses,
    monthlyData
  };
}
