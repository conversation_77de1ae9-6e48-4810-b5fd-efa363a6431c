/**
 * Supabase Integration - Hauptexport
 * Zentrale Exportdatei für alle Supabase-bezogenen Module
 */

// Client und Konfiguration
export { default as supabase, isSupabaseConnected, getDeviceId, isOnline, SyncStatus } from './client';

// TypeScript-Typen
export type { Database, Tables, TablesInsert, TablesUpdate } from './types';

// Storage-Adapter
export { SupabaseAdapter, supabaseAdapter } from './adapter';

// Sync-Service
export { SyncService, syncService } from '../sync/sync-service';
export type { SyncResult, SyncSettings } from '../sync/sync-service';

// Hybrid Storage
export { HybridStorageAdapter, hybridStorageAdapter } from '../storage/hybrid-adapter';
export type { SyncOptions } from '../storage/hybrid-adapter';
