/**
 * Supabase Storage Adapter
 * Implementiert das StorageAdapter Interface für Supabase
 */

import { StorageAdapter } from '../storage/adapter';
import { supabase, getDeviceId, isOnline } from './client';
import { Database, Tables, TablesInsert, TablesUpdate } from './types';

export class SupabaseAdapter implements StorageAdapter {
  private deviceId: string;
  private categoryIdMapping: Map<string, number> = new Map(); // LocalStorage ID → Supabase ID
  private categoryNameMapping: Map<string, number> = new Map(); // Category Name → Supabase ID

  constructor() {
    this.deviceId = getDeviceId();
  }

  /**
   * Liest einen Wert aus Supabase
   * @param key Der Tabellenname (z.B. 'categories', 'transactions')
   * @returns Array der Entitäten oder null
   */
  async getItem(key: string): Promise<any> {
    if (!isOnline()) {
      throw new Error('Keine Internetverbindung verfügbar');
    }

    try {
      const { data, error } = await supabase
        .from(key as keyof Database['public']['Tables'])
        .select('*')
        .order('id', { ascending: true });

      if (error) {
        console.error(`Fehler beim Lesen aus Supabase (${key}):`, error);
        throw error;
      }

      // Konvertiere Supabase-Format zu lokalem Format
      return this.convertFromSupabase(data || [], key);
    } catch (error) {
      console.error(`Fehler beim Lesen aus Supabase (${key}):`, error);
      throw error;
    }
  }

  /**
   * Speichert einen Wert in Supabase
   * @param key Der Tabellenname
   * @param value Array der zu speichernden Entitäten
   */
  async setItem(key: string, value: any): Promise<void> {
    if (!isOnline()) {
      throw new Error('Keine Internetverbindung verfügbar');
    }

    try {
      // Lösche alle bestehenden Einträge für dieses Gerät
      await this.clearTable(key);

      if (!Array.isArray(value) || value.length === 0) {
        return; // Nichts zu speichern
      }

      // Konvertiere zu Supabase-Format und füge device_id hinzu
      const supabaseData = this.convertToSupabase(value, key);

      // Debug: Logge die Daten vor dem Senden
      console.log(`${key}: Sende ${supabaseData.length} Einträge zu Supabase:`, {
        tableName: key,
        sampleData: supabaseData.slice(0, 1),
        allFields: supabaseData.length > 0 ? Object.keys(supabaseData[0]) : []
      });

      // Einfache Lösung: Lösche bestehende Daten für dieses Device, dann füge neue hinzu
      await supabase
        .from(key as keyof Database['public']['Tables'])
        .delete()
        .eq('device_id', this.deviceId);

      // Für Categories: Verwende SQL INSERT mit spezifischen IDs
      if (key === 'categories') {
        try {
          console.log(`🔍 Categories Debug - ${supabaseData.length} Categories sollen eingefügt werden:`);
          supabaseData.forEach((c, index) => {
            console.log(`  ${index + 1}. ID: ${c.id}, Name: ${c.name}, Type: ${c.type}`);
          });

          let successCount = 0;
          let errorCount = 0;

          // Füge Categories einzeln mit ihren ursprünglichen IDs ein
          for (const category of supabaseData) {
            try {
              const { error: insertError } = await supabase
                .from('categories')
                .insert({
                  id: category.id,
                  name: category.name,
                  type: category.type,
                  color: category.color,
                  device_id: category.device_id,
                  created_at: category.created_at,
                  updated_at: category.updated_at
                });

              if (insertError) {
                console.error(`❌ Category ${category.id} (${category.name}) FEHLER:`, insertError);
                errorCount++;
              } else {
                console.log(`✅ Category eingefügt: ${category.id} - ${category.name}`);
                successCount++;
              }
            } catch (singleError) {
              console.error(`❌ Exception bei Category ${category.id}:`, singleError);
              errorCount++;
            }
          }

          console.log(`📊 Category-Insert Ergebnis: ${successCount} erfolgreich, ${errorCount} Fehler`);

          if (errorCount > 0) {
            console.error(`⚠️ ${errorCount} Categories konnten nicht eingefügt werden!`);
          }

          return;
        } catch (error) {
          console.error(`Fehler beim Category-Insert:`, error);
          throw error;
        }
      }

      // Für andere Tabellen: Normaler Insert
      const { data, error } = await supabase
        .from(key as keyof Database['public']['Tables'])
        .insert(supabaseData)
        .select();

      if (error) {
        console.error(`Fehler beim Schreiben in Supabase (${key}):`, {
          error,
          message: error.message,
          details: error.details,
          hint: error.hint,
          code: error.code,
          dataLength: supabaseData.length,
          sampleData: supabaseData.slice(0, 2) // Erste 2 Einträge für Debug
        });
        throw error;
      }

      // Kein Category-ID-Mapping mehr nötig - Supabase verwendet die gleichen IDs
      if (key === 'categories' && data) {
        console.log(`Categories erfolgreich synchronisiert: ${data.length} Einträge`);
      }

      console.log(`${key}: ${value.length} Entitäten in Supabase gespeichert`);
    } catch (error: any) {
      // Detaillierte Fehleranalyse
      const errorDetails = {
        tableName: key,
        errorType: typeof error,
        errorConstructor: error?.constructor?.name,
        errorMessage: error?.message || 'No message',
        errorCode: error?.code || 'No code',
        errorDetails: error?.details || 'No details',
        errorHint: error?.hint || 'No hint',
        errorString: String(error),
        errorJSON: JSON.stringify(error, null, 2),
        dataLength: value?.length || 0,
        sampleData: value?.slice(0, 1) || [],
        deviceId: this.deviceId
      };

      console.error(`Fehler beim Schreiben in Supabase (${key}) - Catch Block:`, errorDetails);

      // Versuche, den ursprünglichen Supabase-Fehler zu extrahieren
      if (error?.error) {
        console.error(`Supabase-Fehler Details:`, error.error);
      }

      throw error;
    }
  }

  /**
   * Entfernt einen Wert aus Supabase
   * @param key Der Tabellenname
   */
  async removeItem(key: string): Promise<void> {
    await this.clearTable(key);
  }

  /**
   * Prüft, ob ein Wert in Supabase existiert
   * @param key Der Tabellenname
   * @returns true, wenn Daten vorhanden sind
   */
  async hasItem(key: string): Promise<boolean> {
    if (!isOnline()) {
      return false;
    }

    try {
      const { count, error } = await supabase
        .from(key as keyof Database['public']['Tables'])
        .select('*', { count: 'exact', head: true });

      if (error) {
        console.error(`Fehler beim Prüfen von Supabase (${key}):`, error);
        return false;
      }

      return (count || 0) > 0;
    } catch (error) {
      console.error(`Fehler beim Prüfen von Supabase (${key}):`, error);
      return false;
    }
  }

  /**
   * Löscht alle Werte aus Supabase
   */
  async clear(): Promise<void> {
    if (!isOnline()) {
      throw new Error('Keine Internetverbindung verfügbar');
    }

    const tables = ['categories', 'transactions', 'savings_goals', 'investments', 'account_balances', 'settings'];
    
    for (const table of tables) {
      await this.clearTable(table);
    }

    console.log('Alle Supabase-Daten gelöscht');
  }

  /**
   * Löscht alle Einträge einer Tabelle
   * @param tableName Name der Tabelle
   */
  private async clearTable(tableName: string): Promise<void> {
    try {
      const { error } = await supabase
        .from(tableName as keyof Database['public']['Tables'])
        .delete()
        .neq('id', 0); // Lösche alle Einträge

      if (error) {
        console.error(`Fehler beim Löschen der Tabelle ${tableName}:`, error);
        throw error;
      }
    } catch (error) {
      console.error(`Fehler beim Löschen der Tabelle ${tableName}:`, error);
      throw error;
    }
  }

  /**
   * Konvertiert lokale Daten zu Supabase-Format
   * @param data Lokale Daten
   * @param tableName Tabellenname
   * @returns Supabase-kompatible Daten
   */
  private convertToSupabase(data: any[], tableName: string): any[] {
    return data.map(item => {
      const supabaseItem = {
        ...item,
        device_id: this.deviceId,
        created_at: item.created_at || new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      // Spezielle Konvertierung für verschiedene Tabellen
      if (tableName === 'transactions') {
        // Für Transactions: Debug und verwende die gleiche Category-ID
        if (item.categoryId) {
          console.log(`🔍 Transaction Debug:`, {
            description: item.description,
            categoryId: item.categoryId,
            amount: item.amount,
            date: item.date
          });
          supabaseItem.category_id = item.categoryId;
          delete supabaseItem.categoryId;
          console.log(`Transaction Category ID: ${item.categoryId} → ${item.categoryId} (direkt)`);
        } else {
          console.error(`❌ Transaction ohne categoryId:`, item);
        }
      }

      if (tableName === 'savings_goals') {
        // Konvertiere camelCase zu snake_case
        if (item.targetAmount) {
          supabaseItem.target_amount = item.targetAmount;
          delete supabaseItem.targetAmount;
        }
        if (item.currentAmount) {
          supabaseItem.current_amount = item.currentAmount;
          delete supabaseItem.currentAmount;
        }
        if (item.targetDate) {
          supabaseItem.target_date = item.targetDate;
          delete supabaseItem.targetDate;
        }
      }

      if (tableName === 'investments') {
        // Für Investments: Verwende die gleiche Category-ID (kein Mapping nötig)
        if (item.categoryId) {
          supabaseItem.category_id = item.categoryId;
          delete supabaseItem.categoryId;
          console.log(`Investment Category ID: ${item.categoryId} → ${item.categoryId} (direkt)`);
        }
        if (item.initialInvestment) {
          supabaseItem.initial_investment = item.initialInvestment;
          delete supabaseItem.initialInvestment;
        }
        if (item.currentValue) {
          supabaseItem.current_value = item.currentValue;
          delete supabaseItem.currentValue;
        }
        if (item.purchaseDate) {
          supabaseItem.purchase_date = item.purchaseDate;
          delete supabaseItem.purchaseDate;
        }
        if (item.lastUpdateDate) {
          supabaseItem.last_update_date = item.lastUpdateDate;
          delete supabaseItem.lastUpdateDate;
        }
      }

      if (tableName === 'account_balances') {
        // Konvertiere camelCase zu snake_case
        if (item.lastUpdated) {
          supabaseItem.last_updated = item.lastUpdated;
          delete supabaseItem.lastUpdated;
        }
      }

      // Entferne undefined-Werte, aber BEHALTE die ID für Categories
      Object.keys(supabaseItem).forEach(key => {
        if (supabaseItem[key] === undefined) {
          delete supabaseItem[key];
        }
        // Für Categories: Behalte die ID, damit Supabase die gleichen IDs verwendet
        if (key === 'id' && tableName !== 'categories') {
          delete supabaseItem[key];
        }
      });

      return supabaseItem;
    });
  }

  /**
   * Konvertiert Supabase-Daten zu lokalem Format
   * @param data Supabase-Daten
   * @param tableName Tabellenname
   * @returns Lokale Daten
   */
  private convertFromSupabase(data: any[], tableName: string): any[] {
    return data.map(item => {
      // Entferne Supabase-spezifische Felder für lokale Kompatibilität
      const { device_id, created_at, updated_at, ...localItem } = item;

      // Konvertiere snake_case zurück zu camelCase
      if (tableName === 'transactions' && item.category_id) {
        localItem.categoryId = item.category_id;
        delete localItem.category_id;
      }

      if (tableName === 'savings_goals') {
        if (item.target_amount) {
          localItem.targetAmount = item.target_amount;
          delete localItem.target_amount;
        }
        if (item.current_amount) {
          localItem.currentAmount = item.current_amount;
          delete localItem.current_amount;
        }
        if (item.target_date) {
          localItem.targetDate = item.target_date;
          delete localItem.target_date;
        }
      }

      if (tableName === 'investments') {
        if (item.category_id) {
          localItem.categoryId = item.category_id;
          delete localItem.category_id;
        }
        if (item.initial_investment) {
          localItem.initialInvestment = item.initial_investment;
          delete localItem.initial_investment;
        }
        if (item.current_value) {
          localItem.currentValue = item.current_value;
          delete localItem.current_value;
        }
        if (item.purchase_date) {
          localItem.purchaseDate = item.purchase_date;
          delete localItem.purchase_date;
        }
        if (item.last_update_date) {
          localItem.lastUpdateDate = item.last_update_date;
          delete localItem.last_update_date;
        }
      }

      if (tableName === 'account_balances' && item.last_updated) {
        localItem.lastUpdated = item.last_updated;
        delete localItem.last_updated;
      }

      return localItem;
    });
  }

  /**
   * Holt die Device-ID
   */
  getDeviceId(): string {
    return this.deviceId;
  }

  /**
   * Prüft die Verbindung zu Supabase
   */
  async testConnection(): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('categories')
        .select('count', { count: 'exact', head: true });
      
      return !error;
    } catch {
      return false;
    }
  }
}

// Singleton-Instanz des SupabaseAdapter
export const supabaseAdapter = new SupabaseAdapter();
