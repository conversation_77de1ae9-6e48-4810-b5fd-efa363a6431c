/**
 * Supabase Client Configuration
 * Zentrale Konfiguration für die Supabase-Verbindung
 */

import { createClient } from '@supabase/supabase-js';
import { Database } from './types';

// Umgebungsvariablen validieren
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl) {
  throw new Error('NEXT_PUBLIC_SUPABASE_URL ist nicht definiert');
}

if (!supabaseAnonKey) {
  throw new Error('NEXT_PUBLIC_SUPABASE_ANON_KEY ist nicht definiert');
}

// Supabase Client erstellen
export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey, {
  auth: {
    persistSession: false, // Keine Authentifizierung für diese App
  },
  db: {
    schema: 'public',
  },
  global: {
    headers: {
      'X-Client-Info': 'foxflow-budget-manager',
    },
  },
});

// Hilfsfunktionen für Verbindungsstatus
export const isSupabaseConnected = async (): Promise<boolean> => {
  try {
    const { error } = await supabase.from('categories').select('count', { count: 'exact', head: true });
    return !error;
  } catch {
    return false;
  }
};

// Device ID generieren und speichern
export const getDeviceId = (): string => {
  if (typeof window === 'undefined') {
    return 'server-device';
  }

  let deviceId = localStorage.getItem('foxflow_device_id');
  
  if (!deviceId) {
    // Einfache Device ID basierend auf Browser-Eigenschaften
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    ctx!.textBaseline = 'top';
    ctx!.font = '14px Arial';
    ctx!.fillText('Device fingerprint', 2, 2);
    
    const fingerprint = canvas.toDataURL();
    deviceId = btoa(fingerprint).slice(0, 16) + '-' + Date.now().toString(36);
    
    localStorage.setItem('foxflow_device_id', deviceId);
  }
  
  return deviceId;
};

// Netzwerkstatus prüfen
export const isOnline = (): boolean => {
  if (typeof window === 'undefined') {
    return true; // Server-side als online betrachten
  }
  return navigator.onLine;
};

// Sync-Status verwalten
export class SyncStatus {
  private static instance: SyncStatus;
  private listeners: ((status: boolean) => void)[] = [];
  private _isOnline: boolean = true;
  private _lastSync: Date | null = null;

  static getInstance(): SyncStatus {
    if (!SyncStatus.instance) {
      SyncStatus.instance = new SyncStatus();
    }
    return SyncStatus.instance;
  }

  private constructor() {
    if (typeof window !== 'undefined') {
      this._isOnline = navigator.onLine;
      
      window.addEventListener('online', () => {
        this._isOnline = true;
        this.notifyListeners(true);
      });
      
      window.addEventListener('offline', () => {
        this._isOnline = false;
        this.notifyListeners(false);
      });
    }
  }

  get isOnline(): boolean {
    return this._isOnline;
  }

  get lastSync(): Date | null {
    return this._lastSync;
  }

  setLastSync(date: Date): void {
    this._lastSync = date;
  }

  addListener(callback: (status: boolean) => void): void {
    this.listeners.push(callback);
  }

  removeListener(callback: (status: boolean) => void): void {
    this.listeners = this.listeners.filter(listener => listener !== callback);
  }

  private notifyListeners(status: boolean): void {
    this.listeners.forEach(listener => listener(status));
  }
}

export default supabase;
