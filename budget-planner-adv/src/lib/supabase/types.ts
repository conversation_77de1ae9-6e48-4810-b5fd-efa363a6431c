export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      account_balances: {
        Row: {
          balance: number
          created_at: string | null
          device_id: string
          id: number
          last_updated: string
          month: number
          name: string
          type: string
          updated_at: string | null
          year: number
        }
        Insert: {
          balance: number
          created_at?: string | null
          device_id: string
          id?: number
          last_updated: string
          month: number
          name: string
          type: string
          updated_at?: string | null
          year: number
        }
        Update: {
          balance?: number
          created_at?: string | null
          device_id?: string
          id?: number
          last_updated?: string
          month?: number
          name?: string
          type?: string
          updated_at?: string | null
          year?: number
        }
        Relationships: []
      }
      categories: {
        Row: {
          color: string
          created_at: string | null
          device_id: string
          id: number
          name: string
          type: string
          updated_at: string | null
        }
        Insert: {
          color?: string
          created_at?: string | null
          device_id: string
          id?: number
          name: string
          type: string
          updated_at?: string | null
        }
        Update: {
          color?: string
          created_at?: string | null
          device_id?: string
          id?: number
          name?: string
          type?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      transactions: {
        Row: {
          amount: number
          category_id: number
          created_at: string | null
          date: string
          description: string
          device_id: string
          id: number
          notes: string | null
          type: string
          updated_at: string | null
        }
        Insert: {
          amount: number
          category_id: number
          created_at?: string | null
          date: string
          description: string
          device_id: string
          id?: number
          notes?: string | null
          type: string
          updated_at?: string | null
        }
        Update: {
          amount?: number
          category_id?: number
          created_at?: string | null
          date?: string
          description?: string
          device_id?: string
          id?: number
          notes?: string | null
          type?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "transactions_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "categories"
            referencedColumns: ["id"]
          }
        ]
      }
      savings_goals: {
        Row: {
          color: string | null
          created_at: string | null
          current_amount: number | null
          device_id: string
          id: number
          name: string
          notes: string | null
          target_amount: number
          target_date: string | null
          updated_at: string | null
        }
        Insert: {
          color?: string | null
          created_at?: string | null
          current_amount?: number | null
          device_id: string
          id?: number
          name: string
          notes?: string | null
          target_amount: number
          target_date?: string | null
          updated_at?: string | null
        }
        Update: {
          color?: string | null
          created_at?: string | null
          current_amount?: number | null
          device_id?: string
          id?: number
          name?: string
          notes?: string | null
          target_amount?: number
          target_date?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
      investments: {
        Row: {
          category_id: number
          color: string | null
          created_at: string | null
          current_value: number
          device_id: string
          id: number
          initial_investment: number
          last_update_date: string
          name: string
          notes: string | null
          purchase_date: string
          updated_at: string | null
        }
        Insert: {
          category_id: number
          color?: string | null
          created_at?: string | null
          current_value: number
          device_id: string
          id?: number
          initial_investment: number
          last_update_date: string
          name: string
          notes?: string | null
          purchase_date: string
          updated_at?: string | null
        }
        Update: {
          category_id?: number
          color?: string | null
          created_at?: string | null
          current_value?: number
          device_id?: string
          id?: number
          initial_investment?: number
          last_update_date?: string
          name?: string
          notes?: string | null
          purchase_date?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "investments_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "categories"
            referencedColumns: ["id"]
          }
        ]
      }
      settings: {
        Row: {
          created_at: string | null
          device_id: string
          id: number
          key: string
          updated_at: string | null
          value: string | null
        }
        Insert: {
          created_at?: string | null
          device_id: string
          id?: number
          key: string
          updated_at?: string | null
          value?: string | null
        }
        Update: {
          created_at?: string | null
          device_id?: string
          id?: number
          key?: string
          updated_at?: string | null
          value?: string | null
        }
        Relationships: []
      }
      sync_metadata: {
        Row: {
          checksum: string | null
          created_at: string | null
          device_id: string
          id: number
          last_sync: string | null
          record_id: number
          table_name: string
        }
        Insert: {
          checksum?: string | null
          created_at?: string | null
          device_id: string
          id?: number
          last_sync?: string | null
          record_id: number
          table_name: string
        }
        Update: {
          checksum?: string | null
          created_at?: string | null
          device_id?: string
          id?: number
          last_sync?: string | null
          record_id?: number
          table_name?: string
        }
        Relationships: []
      }
    }
    Views: {
      categories_with_stats: {
        Row: {
          color: string | null
          created_at: string | null
          current_month_amount: number | null
          device_id: string | null
          id: number | null
          name: string | null
          total_amount: number | null
          transaction_count: number | null
          type: string | null
          updated_at: string | null
        }
        Relationships: []
      }
      dashboard_stats: {
        Row: {
          current_month_expenses: number | null
          current_month_income: number | null
          current_month_transactions: number | null
          total_investment_value: number | null
          total_savings_current: number | null
          total_savings_target: number | null
        }
        Relationships: []
      }
    }
    Functions: {
      get_changes_since: {
        Args: { since_timestamp?: string }
        Returns: {
          table_name: string
          record_id: number
          operation: string
          data: Json
          updated_at: string
        }[]
      }
      resolve_conflict: {
        Args: {
          p_table_name: string
          p_record_id: number
          p_local_updated_at: string
          p_remote_updated_at: string
        }
        Returns: string
      }
      update_sync_metadata: {
        Args: {
          p_table_name: string
          p_record_id: number
          p_device_id: string
          p_checksum?: string
        }
        Returns: undefined
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

// Hilfstypes für einfachere Verwendung
export type Tables<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Row']
export type TablesInsert<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Insert']
export type TablesUpdate<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Update']
