/**
 * Kompatibilitätsschicht für die alte Datenbankschnittstelle
 * Diese Datei exportiert die gleichen Funktionen wie die alte db.ts,
 * verwendet aber die neue Datenbankschicht
 */

import {
  CategoryService,
  TransactionService,
  SavingsGoalService,
  InvestmentService,
  AccountBalanceService
} from './storage';
import type {
  Category as StorageCategory,
  Transaction as StorageTransaction,
  SavingsGoal as StorageSavingsGoal,
  Investment as StorageInvestment,
  AccountBalance as StorageAccountBalance
} from './storage';

// Prüfen, ob wir in einer Tauri-Umgebung sind
export const isTauri = typeof window !== 'undefined' && 'undefined' !== typeof (window as any).__TAURI__;

// Typen für die Datenbank
export interface Transaction {
  id?: number;
  description: string;
  amount: number;
  date: string;
  categoryId: number;
  type: 'income' | 'expense' | 'business_income' | 'business_expense' | 'investment';
  notes?: string;
}

export interface Category {
  id?: number;
  name: string;
  type: 'income' | 'expense' | 'investment' | 'business_income' | 'business_expense';
  color: string;
}

export interface SavingsGoal {
  id?: number;
  name: string;
  targetAmount: number;
  currentAmount: number;
  targetDate?: string;
  notes?: string;
  color?: string;
}

export interface Investment {
  id?: number;
  name: string;
  categoryId: number;
  initialInvestment: number;
  currentValue: number;
  purchaseDate: string;
  lastUpdateDate: string;
  notes?: string;
  color?: string;
}

export interface AccountBalance {
  id?: number;
  name: string;
  type: 'private' | 'business';
  balance: number;
  month: number;
  year: number;
  lastUpdated: string;
}

// Monatsberichte
export interface MonthlyReport {
  year: number;
  month: number;
  income: number;
  expense: number;
  balance: number;
  categoryBreakdown: {
    categoryId: number;
    categoryName: string;
    categoryColor: string;
    amount: number;
    percentage: number;
    type: 'income' | 'expense' | 'business';
  }[];
}

// WICHTIG: Wir initialisieren die Datenbank NIEMALS automatisch
// und laden NIEMALS Beispieldaten, um echte Nutzerdaten zu schützen

// Konvertierungsfunktionen zwischen DB-Layer und Storage-Layer
function convertToStorageTransaction(transaction: Transaction): StorageTransaction {
  return {
    ...transaction,
    categoryId: transaction.categoryId,
    type: transaction.type as any // Type-Kompatibilität
  };
}

function convertFromStorageTransaction(transaction: StorageTransaction): Transaction {
  return {
    ...transaction,
    categoryId: transaction.categoryId,
    type: transaction.type as any // Type-Kompatibilität
  };
}

function convertToStorageSavingsGoal(savingsGoal: SavingsGoal): StorageSavingsGoal {
  return {
    ...savingsGoal,
    color: savingsGoal.color || '#10B981' // Default-Farbe wenn nicht gesetzt
  };
}

function convertFromStorageSavingsGoal(savingsGoal: StorageSavingsGoal): SavingsGoal {
  return {
    ...savingsGoal,
    color: savingsGoal.color
  };
}

function convertToStorageInvestment(investment: Investment): StorageInvestment {
  return {
    ...investment,
    color: investment.color || '#8B5CF6' // Default-Farbe wenn nicht gesetzt
  };
}

function convertFromStorageInvestment(investment: StorageInvestment): Investment {
  return {
    ...investment,
    color: investment.color
  };
}

// Kategorien
export async function getCategories(type?: string): Promise<Category[]> {
  return await CategoryService.getCategories(type);
}

export async function getCategoryById(id: number): Promise<Category | null> {
  return await CategoryService.getCategoryById(id);
}

export async function getCategoriesByIds(ids: number[]): Promise<Record<number, Category>> {
  return await CategoryService.getCategoriesByIds(ids);
}

export async function createCategory(category: Category): Promise<number> {
  return await CategoryService.createCategory(category);
}

export async function updateCategory(category: Category): Promise<boolean> {
  return await CategoryService.updateCategory(category);
}

export async function deleteCategory(id: number): Promise<boolean> {
  return await CategoryService.deleteCategory(id);
}

// Transaktionen
export async function getTransactions(filters?: {
  startDate?: string;
  endDate?: string;
  categoryId?: number;
  type?: 'income' | 'expense' | 'business_income' | 'business_expense' | 'investment' | 'all';
  minAmount?: number;
  maxAmount?: number;
  searchTerm?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}): Promise<Transaction[]> {
  const storageFilters = filters ? {
    ...filters,
    sortBy: filters.sortBy as 'date' | 'amount' | 'description' | undefined
  } : undefined;
  const storageTransactions = await TransactionService.getTransactions(storageFilters);
  return storageTransactions.map(convertFromStorageTransaction);
}

export async function getTransactionById(id: number): Promise<Transaction | null> {
  const storageTransaction = await TransactionService.getTransactionById(id);
  return storageTransaction ? convertFromStorageTransaction(storageTransaction) : null;
}

export async function createTransaction(transaction: Transaction): Promise<number> {
  const storageTransaction = convertToStorageTransaction(transaction);
  return await TransactionService.createTransaction(storageTransaction);
}

export async function updateTransaction(transaction: Transaction): Promise<boolean> {
  const storageTransaction = convertToStorageTransaction(transaction);
  return await TransactionService.updateTransaction(storageTransaction);
}

export async function deleteTransaction(id: number): Promise<boolean> {
  return await TransactionService.deleteTransaction(id);
}

// Sparziele
export async function getSavingsGoals(): Promise<SavingsGoal[]> {
  const storageSavingsGoals = await SavingsGoalService.getSavingsGoals();
  return storageSavingsGoals.map(convertFromStorageSavingsGoal);
}

export async function getSavingsGoalById(id: number): Promise<SavingsGoal | null> {
  const storageSavingsGoal = await SavingsGoalService.getSavingsGoalById(id);
  return storageSavingsGoal ? convertFromStorageSavingsGoal(storageSavingsGoal) : null;
}

export async function createSavingsGoal(savingsGoal: SavingsGoal): Promise<number> {
  const storageSavingsGoal = convertToStorageSavingsGoal(savingsGoal);
  return await SavingsGoalService.createSavingsGoal(storageSavingsGoal);
}

export async function updateSavingsGoal(savingsGoal: SavingsGoal): Promise<boolean> {
  const storageSavingsGoal = convertToStorageSavingsGoal(savingsGoal);
  return await SavingsGoalService.updateSavingsGoal(storageSavingsGoal);
}

export async function deleteSavingsGoal(id: number): Promise<boolean> {
  return await SavingsGoalService.deleteSavingsGoal(id);
}

export async function updateSavingsGoalAmount(id: number, amount: number): Promise<boolean> {
  return await SavingsGoalService.updateSavingsGoalAmount(id, amount);
}

// Investments
export async function getInvestments(): Promise<Investment[]> {
  const storageInvestments = await InvestmentService.getInvestments();
  return storageInvestments.map(convertFromStorageInvestment);
}

export async function getInvestmentById(id: number): Promise<Investment | null> {
  const storageInvestment = await InvestmentService.getInvestmentById(id);
  return storageInvestment ? convertFromStorageInvestment(storageInvestment) : null;
}

export async function createInvestment(investment: Investment): Promise<number> {
  const storageInvestment = convertToStorageInvestment(investment);
  return await InvestmentService.createInvestment(storageInvestment);
}

export async function updateInvestment(investment: Investment): Promise<boolean> {
  const storageInvestment = convertToStorageInvestment(investment);
  return await InvestmentService.updateInvestment(storageInvestment);
}

export async function deleteInvestment(id: number): Promise<boolean> {
  return await InvestmentService.deleteInvestment(id);
}

export async function updateInvestmentValue(id: number, newValue: number): Promise<boolean> {
  return await InvestmentService.updateInvestmentValue(id, newValue);
}

// Kontostände
export async function getAccountBalances(): Promise<AccountBalance[]> {
  return await AccountBalanceService.getAccountBalances();
}

export async function getAccountBalancesByMonth(month: number, year: number): Promise<AccountBalance[]> {
  return await AccountBalanceService.getAccountBalancesByMonth(month, year);
}

export async function getAccountBalanceById(id: number): Promise<AccountBalance | null> {
  return await AccountBalanceService.getAccountBalanceById(id);
}

export async function createAccountBalance(accountBalance: AccountBalance): Promise<number> {
  return await AccountBalanceService.createAccountBalance(accountBalance);
}

export async function updateAccountBalance(accountBalance: AccountBalance): Promise<boolean> {
  return await AccountBalanceService.updateAccountBalance(accountBalance);
}

export async function deleteAccountBalance(id: number): Promise<boolean> {
  return await AccountBalanceService.deleteAccountBalance(id);
}

export async function getCurrentAccountBalance(type: 'private' | 'business', month: number, year: number): Promise<AccountBalance | null> {
  return await AccountBalanceService.getCurrentAccountBalance(type, month, year);
}

export async function updateOrCreateAccountBalance(type: 'private' | 'business', month: number, year: number, balance: number): Promise<boolean> {
  return await AccountBalanceService.updateOrCreateAccountBalance(type, month, year, balance);
}

// Fehlende Funktionen für Reports
export async function getMonthlyReport(month: number, year: number): Promise<MonthlyReport | null> {
  // Implementierung für Monatsberichte
  const transactions = await getTransactions({
    startDate: `${year}-${month.toString().padStart(2, '0')}-01`,
    endDate: `${year}-${month.toString().padStart(2, '0')}-31`
  });

  const categories = await getCategories();
  const categoryMap = categories.reduce((acc, cat) => {
    acc[cat.id!] = cat;
    return acc;
  }, {} as Record<number, Category>);

  const income = transactions
    .filter(t => t.type === 'income' || t.type === 'business_income')
    .reduce((sum, t) => sum + t.amount, 0);

  const expense = transactions
    .filter(t => t.type === 'expense' || t.type === 'business_expense' || t.type === 'investment')
    .reduce((sum, t) => sum + t.amount, 0);

  const categoryBreakdown = Object.values(
    transactions.reduce((acc, transaction) => {
      const category = categoryMap[transaction.categoryId];
      if (!category) return acc;

      const key = transaction.categoryId;
      if (!acc[key]) {
        acc[key] = {
          categoryId: transaction.categoryId,
          categoryName: category.name,
          categoryColor: category.color,
          amount: 0,
          percentage: 0,
          type: transaction.type as any
        };
      }
      acc[key].amount += transaction.amount;
      return acc;
    }, {} as Record<number, any>)
  );

  // Prozentsätze berechnen
  const totalAmount = income + expense;
  categoryBreakdown.forEach(item => {
    item.percentage = totalAmount > 0 ? (item.amount / totalAmount) * 100 : 0;
  });

  return {
    year,
    month,
    income,
    expense,
    balance: income - expense,
    categoryBreakdown
  };
}

export async function getMonthlyReports(startMonth: number, startYear: number, endMonth: number, endYear: number): Promise<MonthlyReport[]> {
  const reports: MonthlyReport[] = [];

  let currentMonth = startMonth;
  let currentYear = startYear;

  while (currentYear < endYear || (currentYear === endYear && currentMonth <= endMonth)) {
    const report = await getMonthlyReport(currentMonth, currentYear);
    if (report) {
      reports.push(report);
    }

    currentMonth++;
    if (currentMonth > 12) {
      currentMonth = 1;
      currentYear++;
    }
  }

  return reports;
}

export async function compareMonths(month1: number, year1: number, month2: number, year2: number): Promise<{
  month1: MonthlyReport | null;
  month2: MonthlyReport | null;
  comparison: {
    incomeDiff: number;
    expenseDiff: number;
    balanceDiff: number;
  };
}> {
  const report1 = await getMonthlyReport(month1, year1);
  const report2 = await getMonthlyReport(month2, year2);

  return {
    month1: report1,
    month2: report2,
    comparison: {
      incomeDiff: (report1?.income || 0) - (report2?.income || 0),
      expenseDiff: (report1?.expense || 0) - (report2?.expense || 0),
      balanceDiff: (report1?.balance || 0) - (report2?.balance || 0)
    }
  };
}

// Reset-Funktion für Debug-Panel
export async function resetDatabase(): Promise<void> {
  // Diese Funktion sollte nur für Debug-Zwecke verwendet werden
  console.warn('resetDatabase wurde aufgerufen - diese Funktion ist deprecated');
}