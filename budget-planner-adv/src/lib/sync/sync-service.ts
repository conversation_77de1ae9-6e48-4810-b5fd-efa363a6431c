/**
 * Sync Service für FoxFlow
 * Verwaltet die Synchronisation zwischen Geräten über Supabase
 */

import { hybridStorageAdapter, SyncOptions } from '../storage/hybrid-adapter';
import { isOnline, SyncStatus, getDeviceId } from '../supabase/client';

export interface SyncResult {
  success: boolean;
  tablesSync: Record<string, { success: boolean; error?: string; recordCount?: number }>;
  totalRecords: number;
  duration: number;
  timestamp: Date;
}

export interface SyncSettings {
  autoSync: boolean;
  syncInterval: number; // in milliseconds
  syncOnStartup: boolean;
  syncOnNetworkReconnect: boolean;
}

export class SyncService {
  private static instance: SyncService;
  private syncStatus: SyncStatus;
  private autoSyncInterval: NodeJS.Timeout | null = null;
  private settings: SyncSettings;

  static getInstance(): SyncService {
    if (!SyncService.instance) {
      SyncService.instance = new SyncService();
    }
    return SyncService.instance;
  }

  private constructor() {
    this.syncStatus = SyncStatus.getInstance();
    this.settings = {
      autoSync: true,
      syncInterval: parseInt(process.env.NEXT_PUBLIC_SYNC_INTERVAL || '30000'),
      syncOnStartup: true,
      syncOnNetworkReconnect: true
    };

    this.initializeEventListeners();
  }

  /**
   * Initialisiert Event-Listener für automatischen Sync
   */
  private initializeEventListeners(): void {
    if (typeof window === 'undefined') return;

    // Sync bei Netzwerk-Reconnect
    this.syncStatus.addListener((isOnline) => {
      if (isOnline && this.settings.syncOnNetworkReconnect) {
        console.log('Netzwerk wieder verfügbar - starte Sync...');
        this.performSync({ direction: 'bidirectional' }).catch(console.error);
      }
    });

    // Sync beim Verlassen der Seite
    window.addEventListener('beforeunload', () => {
      if (isOnline()) {
        this.performSync({ direction: 'push' }).catch(console.error);
      }
    });

    // Sync bei Fokus-Wiederherstellung
    window.addEventListener('focus', () => {
      if (isOnline() && this.settings.autoSync) {
        this.performSync({ direction: 'pull' }).catch(console.error);
      }
    });
  }

  /**
   * Startet den Sync-Service
   */
  async start(): Promise<void> {
    console.log('Sync-Service wird gestartet...');

    // Initial-Sync beim Start
    if (this.settings.syncOnStartup && isOnline()) {
      try {
        await this.performSync({ direction: 'bidirectional' });
        console.log('Initial-Sync erfolgreich abgeschlossen');
      } catch (error) {
        console.error('Initial-Sync fehlgeschlagen:', error);
      }
    }

    // Auto-Sync starten
    if (this.settings.autoSync) {
      this.startAutoSync();
    }
  }

  /**
   * Stoppt den Sync-Service
   */
  stop(): void {
    console.log('Sync-Service wird gestoppt...');
    this.stopAutoSync();
  }

  /**
   * Startet den automatischen Sync
   */
  private startAutoSync(): void {
    if (this.autoSyncInterval) {
      clearInterval(this.autoSyncInterval);
    }

    this.autoSyncInterval = setInterval(async () => {
      if (isOnline()) {
        try {
          await this.performSync({ direction: 'bidirectional' });
        } catch (error) {
          console.warn('Auto-Sync fehlgeschlagen:', error);
        }
      }
    }, this.settings.syncInterval);

    console.log(`Auto-Sync gestartet (Intervall: ${this.settings.syncInterval}ms)`);
  }

  /**
   * Stoppt den automatischen Sync
   */
  private stopAutoSync(): void {
    if (this.autoSyncInterval) {
      clearInterval(this.autoSyncInterval);
      this.autoSyncInterval = null;
      console.log('Auto-Sync gestoppt');
    }
  }

  /**
   * Führt einen manuellen Sync durch
   * @param options Sync-Optionen
   * @returns Sync-Ergebnis
   */
  async performSync(options: SyncOptions = {}): Promise<SyncResult> {
    const startTime = Date.now();
    const timestamp = new Date();
    
    console.log('Starte Sync-Vorgang...', options);

    if (!isOnline()) {
      throw new Error('Sync erfordert eine Internetverbindung');
    }

    const result: SyncResult = {
      success: false,
      tablesSync: {},
      totalRecords: 0,
      duration: 0,
      timestamp
    };

    try {
      await hybridStorageAdapter.sync(options);
      
      // Sammle Statistiken
      const tables = options.tables || ['categories', 'transactions', 'savings_goals', 'investments', 'account_balances', 'settings'];
      
      for (const table of tables) {
        try {
          const data = await hybridStorageAdapter.getItem(table);
          const recordCount = Array.isArray(data) ? data.length : 0;
          
          result.tablesSync[table] = {
            success: true,
            recordCount
          };
          result.totalRecords += recordCount;
        } catch (error) {
          result.tablesSync[table] = {
            success: false,
            error: error instanceof Error ? error.message : 'Unbekannter Fehler'
          };
        }
      }

      result.success = Object.values(result.tablesSync).every(t => t.success);
      result.duration = Date.now() - startTime;

      console.log('Sync erfolgreich abgeschlossen:', result);
      return result;

    } catch (error) {
      result.success = false;
      result.duration = Date.now() - startTime;
      
      console.error('Sync fehlgeschlagen:', error);
      throw error;
    }
  }

  /**
   * Aktualisiert die Sync-Einstellungen
   * @param newSettings Neue Einstellungen
   */
  updateSettings(newSettings: Partial<SyncSettings>): void {
    this.settings = { ...this.settings, ...newSettings };
    
    // Auto-Sync neu starten wenn sich das Intervall geändert hat
    if (newSettings.autoSync !== undefined || newSettings.syncInterval !== undefined) {
      if (this.settings.autoSync) {
        this.startAutoSync();
      } else {
        this.stopAutoSync();
      }
    }

    console.log('Sync-Einstellungen aktualisiert:', this.settings);
  }

  /**
   * Gibt die aktuellen Sync-Einstellungen zurück
   */
  getSettings(): SyncSettings {
    return { ...this.settings };
  }

  /**
   * Gibt den aktuellen Sync-Status zurück
   */
  getStatus(): {
    isOnline: boolean;
    lastSync: Date | null;
    autoSyncActive: boolean;
    deviceId: string;
    syncInProgress: string[];
  } {
    const hybridStatus = hybridStorageAdapter.getSyncStatus();
    
    return {
      isOnline: hybridStatus.isOnline,
      lastSync: hybridStatus.lastSync,
      autoSyncActive: this.autoSyncInterval !== null,
      deviceId: getDeviceId(),
      syncInProgress: hybridStatus.syncInProgress
    };
  }

  /**
   * Testet die Sync-Verbindung
   */
  async testConnection(): Promise<{ localStorage: boolean; supabase: boolean }> {
    return await hybridStorageAdapter.testConnection();
  }

  /**
   * Erzwingt einen vollständigen Sync (überschreibt lokale Daten mit Remote-Daten)
   */
  async forceFullSync(): Promise<SyncResult> {
    console.log('Erzwinge vollständigen Sync...');
    
    return await this.performSync({
      forceSync: true,
      direction: 'pull' // Überschreibe lokale Daten mit Remote-Daten
    });
  }

  /**
   * Pusht alle lokalen Daten zu Supabase (überschreibt Remote-Daten)
   */
  async pushAllData(): Promise<SyncResult> {
    console.log('Pushe alle lokalen Daten zu Supabase...');
    
    return await this.performSync({
      forceSync: true,
      direction: 'push' // Überschreibe Remote-Daten mit lokalen Daten
    });
  }
}

// Singleton-Export
export const syncService = SyncService.getInstance();
