import { Entity } from './repository';

/**
 * Kategorie-Entität
 */
export interface Category extends Entity {
  name: string;
  type: 'income' | 'expense' | 'business_income' | 'business_expense' | 'investment';
  color: string;
}

/**
 * Transaktion-Entität
 */
export interface Transaction extends Entity {
  description: string;
  amount: number;
  date: string;
  categoryId: number;
  type: 'income' | 'expense' | 'investment' | 'business_income' | 'business_expense';
  notes?: string;
}

/**
 * Sparziel-Entität
 */
export interface SavingsGoal extends Entity {
  name: string;
  targetAmount: number;
  currentAmount: number;
  targetDate?: string;
  notes?: string;
  color: string;
}

/**
 * Investment-Entität
 */
export interface Investment extends Entity {
  name: string;
  categoryId: number;
  initialInvestment: number;
  currentValue: number;
  purchaseDate: string;
  lastUpdateDate: string;
  notes?: string;
  color: string;
}

/**
 * Kontostand-Entität
 */
export interface AccountBalance extends Entity {
  name: string;
  type: 'private' | 'business';
  balance: number;
  month: number;
  year: number;
  lastUpdated: string;
}

/**
 * Einstellungen-Entität
 */
export interface Settings extends Entity {
  currency: string;
  language: string;
  theme: string;
  startMonth: number;
  startYear: number;
}
