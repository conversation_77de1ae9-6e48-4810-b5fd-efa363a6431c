/**
 * Hybrid Storage Adapter
 * Kombiniert LocalStorage und Supabase für optimale Performance und Sync
 */

import { StorageAdapter, localStorageAdapter } from './adapter';
import { supabaseAdapter } from '../supabase/adapter';
import { isOnline, SyncStatus } from '../supabase/client';

export interface SyncOptions {
  forceSync?: boolean;
  direction?: 'push' | 'pull' | 'bidirectional';
  tables?: string[];
}

export class HybridStorageAdapter implements StorageAdapter {
  private syncStatus: SyncStatus;
  private syncInProgress: Set<string> = new Set();

  constructor() {
    this.syncStatus = SyncStatus.getInstance();
  }

  /**
   * Liest einen Wert - primär aus LocalStorage, mit Fallback auf Supabase
   * @param key Der Schlüssel des zu lesenden Werts
   * @returns Der gespeicherte Wert oder null
   */
  async getItem(key: string): Promise<any> {
    try {
      // Versuche zuerst LocalStorage
      const localData = await localStorageAdapter.getItem(key);
      
      if (localData && localData.length > 0) {
        // Starte Background-Sync wenn online
        if (isOnline() && !this.syncInProgress.has(key)) {
          this.backgroundSync(key).catch(console.error);
        }
        return localData;
      }

      // Fallback auf Supabase wenn LocalStorage leer ist
      if (isOnline()) {
        console.log(`${key}: LocalStorage leer, lade von Supabase...`);
        const supabaseData = await supabaseAdapter.getItem(key);
        
        if (supabaseData && supabaseData.length > 0) {
          // Speichere in LocalStorage für zukünftige Zugriffe
          await localStorageAdapter.setItem(key, supabaseData);
          return supabaseData;
        }
      }

      return localData || [];
    } catch (error) {
      console.error(`Fehler beim Lesen von ${key}:`, error);
      // Fallback auf LocalStorage bei Fehlern
      return await localStorageAdapter.getItem(key) || [];
    }
  }

  /**
   * Speichert einen Wert in LocalStorage und synct zu Supabase
   * @param key Der Schlüssel
   * @param value Der zu speichernde Wert
   */
  async setItem(key: string, value: any): Promise<void> {
    try {
      // Speichere immer zuerst lokal für sofortige Verfügbarkeit
      await localStorageAdapter.setItem(key, value);
      
      // Sync zu Supabase wenn online
      if (isOnline()) {
        try {
          await supabaseAdapter.setItem(key, value);
          this.syncStatus.setLastSync(new Date());
          console.log(`${key}: Erfolgreich zu Supabase synchronisiert`);
        } catch (error) {
          console.warn(`${key}: Supabase-Sync fehlgeschlagen, Daten nur lokal gespeichert:`, error);
        }
      } else {
        console.log(`${key}: Offline - Daten nur lokal gespeichert`);
      }
    } catch (error) {
      console.error(`Fehler beim Speichern von ${key}:`, error);
      throw error;
    }
  }

  /**
   * Entfernt einen Wert aus beiden Speichern
   * @param key Der Schlüssel des zu entfernenden Werts
   */
  async removeItem(key: string): Promise<void> {
    await localStorageAdapter.removeItem(key);
    
    if (isOnline()) {
      try {
        await supabaseAdapter.removeItem(key);
      } catch (error) {
        console.warn(`${key}: Supabase-Löschung fehlgeschlagen:`, error);
      }
    }
  }

  /**
   * Prüft, ob ein Wert existiert (primär LocalStorage)
   * @param key Der zu prüfende Schlüssel
   * @returns true, wenn der Wert existiert
   */
  async hasItem(key: string): Promise<boolean> {
    const hasLocal = await localStorageAdapter.hasItem(key);
    
    if (hasLocal) {
      return true;
    }

    // Prüfe Supabase wenn lokal nichts vorhanden
    if (isOnline()) {
      try {
        return await supabaseAdapter.hasItem(key);
      } catch {
        return false;
      }
    }

    return false;
  }

  /**
   * Löscht alle Werte aus beiden Speichern
   */
  async clear(): Promise<void> {
    await localStorageAdapter.clear();
    
    if (isOnline()) {
      try {
        await supabaseAdapter.clear();
      } catch (error) {
        console.warn('Supabase-Löschung fehlgeschlagen:', error);
      }
    }
  }

  /**
   * Manueller Sync zwischen LocalStorage und Supabase
   * @param options Sync-Optionen
   */
  async sync(options: SyncOptions = {}): Promise<void> {
    if (!isOnline()) {
      throw new Error('Sync erfordert eine Internetverbindung');
    }

    const {
      forceSync = false,
      direction = 'bidirectional',
      tables = ['categories', 'settings', 'account_balances', 'transactions', 'investments'] // Savings Goals temporär entfernt
    } = options;

    console.log(`Starte ${direction}-Sync für Tabellen:`, tables);

    for (const table of tables) {
      if (this.syncInProgress.has(table) && !forceSync) {
        console.log(`${table}: Sync bereits in Bearbeitung, überspringe...`);
        continue;
      }

      this.syncInProgress.add(table);

      try {
        await this.syncTable(table, direction);
      } catch (error) {
        console.error(`Fehler beim Sync von ${table}:`, error);
      } finally {
        this.syncInProgress.delete(table);
      }
    }

    this.syncStatus.setLastSync(new Date());
    console.log('Sync abgeschlossen');
  }

  /**
   * Synct eine einzelne Tabelle
   * @param table Tabellenname
   * @param direction Sync-Richtung
   */
  private async syncTable(table: string, direction: 'push' | 'pull' | 'bidirectional'): Promise<void> {
    try {
      if (direction === 'push' || direction === 'bidirectional') {
        // Push: LocalStorage → Supabase
        const localData = await localStorageAdapter.getItem(table);
        if (localData && localData.length > 0) {
          console.log(`${table}: Pushe ${localData.length} Einträge zu Supabase...`);
          await supabaseAdapter.setItem(table, localData);
          console.log(`${table}: ${localData.length} Einträge zu Supabase gepusht`);
        } else {
          console.log(`${table}: Keine lokalen Daten zum Pushen`);
        }
      }

      if (direction === 'pull' || direction === 'bidirectional') {
        // Pull: Supabase → LocalStorage
        const supabaseData = await supabaseAdapter.getItem(table);
        if (supabaseData && supabaseData.length > 0) {
          await localStorageAdapter.setItem(table, supabaseData);
          console.log(`${table}: ${supabaseData.length} Einträge von Supabase gepullt`);
        }
      }
    } catch (error) {
      console.error(`Fehler beim Sync von Tabelle ${table}:`, error);
      throw error;
    }
  }

  /**
   * Background-Sync für bessere Performance
   * @param table Tabellenname
   */
  private async backgroundSync(table: string): Promise<void> {
    if (this.syncInProgress.has(table)) {
      return;
    }

    this.syncInProgress.add(table);

    try {
      // Einfacher bidirektionaler Sync im Hintergrund
      await this.syncTable(table, 'bidirectional');
    } catch (error) {
      console.warn(`Background-Sync für ${table} fehlgeschlagen:`, error);
    } finally {
      this.syncInProgress.delete(table);
    }
  }

  /**
   * Prüft den Sync-Status
   */
  getSyncStatus(): { isOnline: boolean; lastSync: Date | null; syncInProgress: string[] } {
    return {
      isOnline: isOnline(),
      lastSync: this.syncStatus.lastSync,
      syncInProgress: Array.from(this.syncInProgress)
    };
  }

  /**
   * Testet die Verbindung zu beiden Speichern
   */
  async testConnection(): Promise<{ localStorage: boolean; supabase: boolean }> {
    const localStorage = true; // LocalStorage ist immer verfügbar
    let supabase = false;

    if (isOnline()) {
      try {
        supabase = await supabaseAdapter.testConnection();
      } catch {
        supabase = false;
      }
    }

    return { localStorage, supabase };
  }
}

// Singleton-Instanz des HybridStorageAdapter
export const hybridStorageAdapter = new HybridStorageAdapter();
