import { localStorageAdapter } from './adapter';
import { hybridStorageAdapter } from './hybrid-adapter';
import { Repository } from './repository';
import { Category, Transaction, SavingsGoal, Investment, Settings, AccountBalance } from './models';

// Wähle den Storage-Adapter basierend auf Umgebungsvariablen
const useSupabaseSync = process.env.NEXT_PUBLIC_SYNC_ENABLED === 'true';
const storageAdapter = useSupabaseSync ? hybridStorageAdapter : localStorageAdapter;

console.log(`Storage-Modus: ${useSupabaseSync ? 'Hybrid (LocalStorage + Supabase)' : 'Nur LocalStorage'}`);

// Repositories für die verschiedenen Entitätstypen
export const categoryRepository = new Repository<Category>(storageAdapter, 'categories');
export const transactionRepository = new Repository<Transaction>(storageAdapter, 'transactions');
export const savingsGoalRepository = new Repository<SavingsGoal>(storageAdapter, 'savings_goals');
export const investmentRepository = new Repository<Investment>(storageAdapter, 'investments');
export const settingsRepository = new Repository<Settings>(storageAdapter, 'settings');
export const accountBalanceRepository = new Repository<AccountBalance>(storageAdapter, 'account_balances');

// Kategorie-Service
export const CategoryService = {
  /**
   * Holt alle Kategorien
   * @param type Optional: Filterung nach Kategorietyp
   * @returns Array von Kategorien
   */
  async getCategories(type?: string): Promise<Category[]> {
    const categories = await categoryRepository.getAll();
    const filtered = type ? categories.filter(c => c.type === type) : categories;
    return filtered.sort((a, b) => a.name.localeCompare(b.name));
  },

  /**
   * Holt mehrere Kategorien anhand ihrer IDs in einer einzigen Anfrage
   * @param ids Array von Kategorie-IDs
   * @returns Record mit Kategorie-IDs als Schlüssel und Kategorien als Werte
   */
  async getCategoriesByIds(ids: number[]): Promise<Record<number, Category>> {
    const categories = await categoryRepository.getAll();
    const categoryMap: Record<number, Category> = {};

    // Filtere Kategorien nach den angegebenen IDs
    categories
      .filter(category => ids.includes(Number(category.id)))
      .forEach(category => {
        if (category.id !== undefined) {
          categoryMap[category.id] = category;
        }
      });

    return categoryMap;
  },

  /**
   * Holt eine Kategorie anhand ihrer ID
   * @param id Die ID der Kategorie
   * @returns Die Kategorie oder null
   */
  async getCategoryById(id: number): Promise<Category | null> {
    return await categoryRepository.getById(id);
  },

  /**
   * Erstellt eine neue Kategorie
   * @param category Die zu erstellende Kategorie
   * @returns Die ID der neuen Kategorie
   */
  async createCategory(category: Category): Promise<number> {
    return await categoryRepository.create(category);
  },

  /**
   * Aktualisiert eine Kategorie
   * @param category Die zu aktualisierende Kategorie
   * @returns true bei Erfolg
   */
  async updateCategory(category: Category): Promise<boolean> {
    return await categoryRepository.update(category);
  },

  /**
   * Löscht eine Kategorie
   * @param id Die ID der zu löschenden Kategorie
   * @returns true bei Erfolg
   */
  async deleteCategory(id: number): Promise<boolean> {
    return await categoryRepository.delete(id);
  }
};

// Transaktions-Service
export const TransactionService = {
  /**
   * Holt alle Transaktionen
   * @param filters Optional: Filter für Transaktionen
   * @returns Array von Transaktionen
   */
  async getTransactions(filters?: {
    startDate?: string;
    endDate?: string;
    categoryId?: number;
    type?: 'income' | 'expense' | 'business_income' | 'business_expense' | 'investment' | 'all';
    minAmount?: number;
    maxAmount?: number;
    searchTerm?: string;
    sortBy?: 'date' | 'amount' | 'description';
    sortOrder?: 'asc' | 'desc';
  }): Promise<Transaction[]> {
    let transactions = await transactionRepository.getAll();

    if (filters) {
      // Datum-Filter mit korrektem Vergleich von Datumsangaben
      if (filters.startDate) {
        transactions = transactions.filter(t => {
          // Konvertiere Strings zu Date-Objekten für korrekten chronologischen Vergleich
          const transactionDate = new Date(t.date);
          const startDate = new Date(filters.startDate!);
          return transactionDate >= startDate;
        });
      }

      if (filters.endDate) {
        transactions = transactions.filter(t => {
          // Konvertiere Strings zu Date-Objekten für korrekten chronologischen Vergleich
          const transactionDate = new Date(t.date);
          const endDate = new Date(filters.endDate!);
          return transactionDate <= endDate;
        });
      }

      // Kategorie-Filter
      if (filters.categoryId) {
        transactions = transactions.filter(t => t.categoryId === filters.categoryId);
      }

      // Typ-Filter
      if (filters.type && filters.type !== 'all') {
        transactions = transactions.filter(t => t.type === filters.type);
      }

      // Betrags-Filter
      if (filters.minAmount !== undefined) {
        transactions = transactions.filter(t => t.amount >= filters.minAmount!);
      }

      if (filters.maxAmount !== undefined) {
        transactions = transactions.filter(t => t.amount <= filters.maxAmount!);
      }

      // Suchbegriff-Filter
      if (filters.searchTerm) {
        const searchTermLower = filters.searchTerm.toLowerCase();
        transactions = transactions.filter(t =>
          t.description.toLowerCase().includes(searchTermLower) ||
          (t.notes && t.notes.toLowerCase().includes(searchTermLower))
        );
      }
    }

    // Sortierung
    if (filters?.sortBy) {
      const sortOrder = filters.sortOrder === 'asc' ? 1 : -1;

      switch (filters.sortBy) {
        case 'date':
          transactions = transactions.sort((a, b) => {
            // Konvertiere Strings zu Date-Objekten für korrekten chronologischen Vergleich
            const dateA = new Date(a.date);
            const dateB = new Date(b.date);
            return sortOrder * (dateA.getTime() - dateB.getTime());
          });
          break;
        case 'amount':
          transactions = transactions.sort((a, b) => sortOrder * (a.amount - b.amount));
          break;
        case 'description':
          transactions = transactions.sort((a, b) => sortOrder * a.description.localeCompare(b.description));
          break;
        default:
          transactions = transactions.sort((a, b) => {
            // Konvertiere Strings zu Date-Objekten für korrekten chronologischen Vergleich
            const dateA = new Date(a.date);
            const dateB = new Date(b.date);
            return dateB.getTime() - dateA.getTime();
          });
      }
    } else {
      // Standardsortierung nach Datum (absteigend)
      transactions = transactions.sort((a, b) => {
        // Konvertiere Strings zu Date-Objekten für korrekten chronologischen Vergleich
        const dateA = new Date(a.date);
        const dateB = new Date(b.date);
        return dateB.getTime() - dateA.getTime();
      });
    }

    return transactions;
  },

  /**
   * Holt eine Transaktion anhand ihrer ID
   * @param id Die ID der Transaktion
   * @returns Die Transaktion oder null
   */
  async getTransactionById(id: number): Promise<Transaction | null> {
    return await transactionRepository.getById(id);
  },

  /**
   * Erstellt eine neue Transaktion
   * @param transaction Die zu erstellende Transaktion
   * @returns Die ID der neuen Transaktion
   */
  async createTransaction(transaction: Transaction): Promise<number> {
    return await transactionRepository.create(transaction);
  },

  /**
   * Aktualisiert eine Transaktion
   * @param transaction Die zu aktualisierende Transaktion
   * @returns true bei Erfolg
   */
  async updateTransaction(transaction: Transaction): Promise<boolean> {
    return await transactionRepository.update(transaction);
  },

  /**
   * Löscht eine Transaktion
   * @param id Die ID der zu löschenden Transaktion
   * @returns true bei Erfolg
   */
  async deleteTransaction(id: number): Promise<boolean> {
    return await transactionRepository.delete(id);
  }
};

// Sparziel-Service
export const SavingsGoalService = {
  /**
   * Holt alle Sparziele
   * @returns Array von Sparzielen
   */
  async getSavingsGoals(): Promise<SavingsGoal[]> {
    return await savingsGoalRepository.getAll();
  },

  /**
   * Holt ein Sparziel anhand seiner ID
   * @param id Die ID des Sparziels
   * @returns Das Sparziel oder null
   */
  async getSavingsGoalById(id: number): Promise<SavingsGoal | null> {
    return await savingsGoalRepository.getById(id);
  },

  /**
   * Erstellt ein neues Sparziel
   * @param savingsGoal Das zu erstellende Sparziel
   * @returns Die ID des neuen Sparziels
   */
  async createSavingsGoal(savingsGoal: SavingsGoal): Promise<number> {
    return await savingsGoalRepository.create(savingsGoal);
  },

  /**
   * Aktualisiert ein Sparziel
   * @param savingsGoal Das zu aktualisierende Sparziel
   * @returns true bei Erfolg
   */
  async updateSavingsGoal(savingsGoal: SavingsGoal): Promise<boolean> {
    return await savingsGoalRepository.update(savingsGoal);
  },

  /**
   * Löscht ein Sparziel
   * @param id Die ID des zu löschenden Sparziels
   * @returns true bei Erfolg
   */
  async deleteSavingsGoal(id: number): Promise<boolean> {
    return await savingsGoalRepository.delete(id);
  },

  /**
   * Aktualisiert den Betrag eines Sparziels (Einzahlung)
   * @param id Die ID des Sparziels
   * @param amount Der einzuzahlende Betrag
   * @returns true bei Erfolg, false bei Fehler oder ungültigen Werten
   */
  async updateSavingsGoalAmount(id: number, amount: number): Promise<boolean> {
    const savingsGoal = await savingsGoalRepository.getById(id);
    if (!savingsGoal) return false;

    const currentAmount = Number(savingsGoal.currentAmount);
    const targetAmount = Number(savingsGoal.targetAmount);
    const depositAmount = Number(amount);

    // Validiere die Beträge
    if (isNaN(currentAmount) || isNaN(depositAmount) || isNaN(targetAmount)) {
      console.error("Ungültige Beträge:", { currentAmount, depositAmount, targetAmount });
      return false;
    }

    // Prüfe, ob der Einzahlungsbetrag positiv ist
    if (depositAmount <= 0) {
      console.error("Einzahlungsbetrag muss positiv sein:", depositAmount);
      return false;
    }

    const newAmount = currentAmount + depositAmount;

    // Prüfe, ob der neue Betrag das Ziel überschreitet
    if (newAmount > targetAmount) {
      console.error("Neuer Betrag überschreitet das Sparziel:", { newAmount, targetAmount });
      return false;
    }

    return await savingsGoalRepository.update({
      ...savingsGoal,
      currentAmount: newAmount
    });
  }
};

// Investment-Service
export const InvestmentService = {
  /**
   * Holt alle Investments
   * @returns Array von Investments
   */
  async getInvestments(): Promise<Investment[]> {
    return await investmentRepository.getAll();
  },

  /**
   * Holt ein Investment anhand seiner ID
   * @param id Die ID des Investments
   * @returns Das Investment oder null
   */
  async getInvestmentById(id: number): Promise<Investment | null> {
    return await investmentRepository.getById(id);
  },

  /**
   * Erstellt ein neues Investment
   * @param investment Das zu erstellende Investment
   * @returns Die ID des neuen Investments
   */
  async createInvestment(investment: Investment): Promise<number> {
    return await investmentRepository.create(investment);
  },

  /**
   * Aktualisiert ein Investment
   * @param investment Das zu aktualisierende Investment
   * @returns true bei Erfolg
   */
  async updateInvestment(investment: Investment): Promise<boolean> {
    return await investmentRepository.update(investment);
  },

  /**
   * Löscht ein Investment
   * @param id Die ID des zu löschenden Investments
   * @returns true bei Erfolg
   */
  async deleteInvestment(id: number): Promise<boolean> {
    return await investmentRepository.delete(id);
  },

  /**
   * Aktualisiert den Wert eines Investments
   * @param id Die ID des Investments
   * @param newValue Der neue Wert
   * @returns true bei Erfolg
   */
  async updateInvestmentValue(id: number, newValue: number): Promise<boolean> {
    // Prüfe, ob der neue Wert eine gültige, endliche Zahl ist
    if (!Number.isFinite(newValue) || newValue < 0) return false;

    const investment = await investmentRepository.getById(id);
    if (!investment) return false;

    const today = new Date().toISOString().split('T')[0];

    return await investmentRepository.update({
      ...investment,
      currentValue: newValue,
      lastUpdateDate: today
    });
  }
};

// Kontostand-Service
export const AccountBalanceService = {
  /**
   * Holt alle Kontostände
   * @returns Array von Kontoständen
   */
  async getAccountBalances(): Promise<AccountBalance[]> {
    return await accountBalanceRepository.getAll();
  },

  /**
   * Holt Kontostände für einen bestimmten Monat und Jahr
   * @param month Der Monat (1-12)
   * @param year Das Jahr
   * @returns Array von Kontoständen für den angegebenen Monat und Jahr
   */
  async getAccountBalancesByMonth(month: number, year: number): Promise<AccountBalance[]> {
    const balances = await accountBalanceRepository.getAll();
    return balances.filter(b => b.month === month && b.year === year);
  },

  /**
   * Holt einen Kontostand anhand seiner ID
   * @param id Die ID des Kontostands
   * @returns Der Kontostand oder null
   */
  async getAccountBalanceById(id: number): Promise<AccountBalance | null> {
    return await accountBalanceRepository.getById(id);
  },

  /**
   * Erstellt einen neuen Kontostand
   * @param accountBalance Der zu erstellende Kontostand
   * @returns Die ID des neuen Kontostands
   */
  async createAccountBalance(accountBalance: AccountBalance): Promise<number> {
    return await accountBalanceRepository.create(accountBalance);
  },

  /**
   * Aktualisiert einen Kontostand
   * @param accountBalance Der zu aktualisierende Kontostand
   * @returns true bei Erfolg
   */
  async updateAccountBalance(accountBalance: AccountBalance): Promise<boolean> {
    return await accountBalanceRepository.update(accountBalance);
  },

  /**
   * Löscht einen Kontostand
   * @param id Die ID des zu löschenden Kontostands
   * @returns true bei Erfolg
   */
  async deleteAccountBalance(id: number): Promise<boolean> {
    return await accountBalanceRepository.delete(id);
  },

  /**
   * Holt den aktuellen Kontostand für einen bestimmten Typ (privat oder geschäftlich)
   * @param type Der Typ des Kontos ('private' oder 'business')
   * @param month Der Monat (1-12)
   * @param year Das Jahr
   * @returns Der Kontostand oder null, wenn keiner gefunden wurde
   */
  async getCurrentAccountBalance(type: 'private' | 'business', month: number, year: number): Promise<AccountBalance | null> {
    const balances = await accountBalanceRepository.getAll();
    return balances.find(b => b.type === type && b.month === month && b.year === year) || null;
  },

  /**
   * Aktualisiert oder erstellt einen Kontostand für einen bestimmten Monat und Jahr
   * @param type Der Typ des Kontos ('private' oder 'business')
   * @param month Der Monat (1-12)
   * @param year Das Jahr
   * @param balance Der neue Kontostand
   * @returns true bei Erfolg
   */
  async updateOrCreateAccountBalance(type: 'private' | 'business', month: number, year: number, balance: number): Promise<boolean> {
    const balances = await accountBalanceRepository.getAll();
    const existingBalance = balances.find(b => b.type === type && b.month === month && b.year === year);

    if (existingBalance) {
      return await accountBalanceRepository.update({
        ...existingBalance,
        balance,
        lastUpdated: new Date().toISOString()
      });
    } else {
      const name = type === 'private' ? 'Privatkonto' : 'Geschäftskonto';
      const newId = await accountBalanceRepository.create({
        name,
        type,
        month,
        year,
        balance,
        lastUpdated: new Date().toISOString()
      });
      // Prüfe, ob die Erstellung erfolgreich war (ID > 0)
      return newId > 0;
    }
  }
};