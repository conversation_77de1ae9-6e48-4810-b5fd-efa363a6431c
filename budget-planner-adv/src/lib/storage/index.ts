// Exportiere alle Komponenten der Datenbankschicht
export * from './adapter';
export * from './hybrid-adapter';
export * from './repository';
export * from './models';
export * from './services';

// Exportiere eine Funktion zum Initialisieren der Datenbank
import { localStorageAdapter } from './adapter';
import {
  CategoryService,
  SavingsGoalService,
  TransactionService,
  InvestmentService,
  AccountBalanceService,
  categoryRepository,
  transactionRepository,
  savingsGoalRepository,
  investmentRepository,
  settingsRepository,
  accountBalanceRepository
} from './services';
import { Category, SavingsGoal, Transaction, Investment, AccountBalance } from './models';

// Re-exportiere die Typen explizit
export { Category, SavingsGoal, Transaction, Investment, AccountBalance };

/**
 * Initialisiert die Datenbank (leer, ohne Beispieldaten)
 */
export async function initializeDatabase(): Promise<void> {
  console.log("Initialisiere leere Datenbank...");

  // Keine Beispieldaten mehr, nur leere Datenbank
  console.log("Leere Datenbank initialisiert.");
}

/**
 * Setzt die Datenbank zurück (löscht alle Daten)
 */
export async function resetDatabase(): Promise<void> {
  console.log("Setze Datenbank zurück...");

  await localStorageAdapter.clear();

  console.log("Datenbank zurückgesetzt.");
}

/**
 * Leert die Datenbank vollständig (löscht alle Daten ohne Beispieldaten zu laden)
 */
export async function clearDatabase(): Promise<void> {
  console.log("Leere Datenbank vollständig...");

  // Lösche alle Daten in den Repositories
  await categoryRepository.deleteAll();
  await transactionRepository.deleteAll();
  await savingsGoalRepository.deleteAll();
  await investmentRepository.deleteAll();
  await settingsRepository.deleteAll();
  await accountBalanceRepository.deleteAll();

  // Lösche auch den LocalStorage komplett
  await localStorageAdapter.clear();

  console.log("Datenbank vollständig geleert.");
}
