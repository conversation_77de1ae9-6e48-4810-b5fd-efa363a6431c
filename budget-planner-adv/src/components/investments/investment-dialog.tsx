'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { InvestmentForm } from './investment-form';
import { PlusIcon, PencilIcon } from 'lucide-react';
import { Investment } from '@/lib/db';

interface InvestmentDialogProps {
  onSuccess?: () => void;
  investment?: Investment;
  mode?: 'create' | 'edit';
  trigger?: React.ReactNode;
}

export function InvestmentDialog({ 
  onSuccess, 
  investment, 
  mode = 'create',
  trigger 
}: InvestmentDialogProps) {
  const [open, setOpen] = useState(false);

  const handleSuccess = () => {
    setOpen(false);
    if (onSuccess) {
      onSuccess();
    }
  };

  const isEditMode = mode === 'edit';

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button>
            {isEditMode ? (
              <PencilIcon className="mr-2 h-4 w-4" />
            ) : (
              <PlusIcon className="mr-2 h-4 w-4" />
            )}
            {isEditMode ? 'Bearbeiten' : 'Neues Investment'}
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>
            {isEditMode ? 'Investment bearbeiten' : 'Neues Investment'}
          </DialogTitle>
          <DialogDescription>
            {isEditMode 
              ? 'Bearbeite dein Investment und verfolge die Wertentwicklung.' 
              : 'Erstelle ein neues Investment und verfolge die Wertentwicklung.'}
          </DialogDescription>
        </DialogHeader>
        <InvestmentForm
          investment={investment || null}
          categories={[]} // TODO: Kategorien laden
          onSuccess={handleSuccess}
          onCancel={() => setOpen(false)}
          defaultValues={investment}
          mode={mode}
        />
      </DialogContent>
    </Dialog>
  );
}
