"use client";

import { useState } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { CalendarIcon } from "lucide-react";
import { cn, formatDateForInput } from "@/lib/utils";
import { createInvestment, updateInvestment, type Investment, type Category } from "@/lib/db";

// Schema für die Formularvalidierung
const investmentFormSchema = z.object({
  name: z.string().min(1, {
    message: "Der Name darf nicht leer sein.",
  }),
  categoryId: z.coerce.number({
    required_error: "Bitte wähle eine Kategorie aus.",
    invalid_type_error: "Bitte wähle eine gültige Kategorie aus.",
  }).refine((val) => val > 0, {
    message: "Bitte wähle eine gültige Kategorie aus."
  }),
  initialInvestment: z.coerce.number({
    required_error: "Bitte gib einen Betrag ein.",
    invalid_type_error: "Bitte gib eine gültige Zahl ein.",
  }).positive({
    message: "Der Betrag muss größer als 0 sein.",
  }),
  currentValue: z.coerce.number({
    required_error: "Bitte gib einen Betrag ein.",
    invalid_type_error: "Bitte gib eine gültige Zahl ein.",
  }).min(0, {
    message: "Der Betrag darf nicht negativ sein.",
  }),
  purchaseDate: z.date({
    required_error: "Bitte wähle ein Datum aus.",
  }),
  notes: z.string().optional(),
  color: z.string().optional(),
});

type InvestmentFormValues = z.infer<typeof investmentFormSchema>;

interface InvestmentFormProps {
  investment: Investment | null;
  categories: Category[];
  onCancel: () => void;
  onSuccess: () => void;
  defaultValues?: Investment;
  mode?: 'create' | 'edit';
}

export function InvestmentForm({
  investment,
  categories,
  onCancel,
  onSuccess
}: InvestmentFormProps) {
  // Vordefinierte Farben
  const colorOptions = [
    { value: '#1abc9c', label: 'Türkis' },
    { value: '#16a085', label: 'Dunkelgrün' },
    { value: '#2ecc71', label: 'Grün' },
    { value: '#27ae60', label: 'Smaragdgrün' },
    { value: '#3498db', label: 'Blau' },
    { value: '#2980b9', label: 'Dunkelblau' },
    { value: '#9b59b6', label: 'Lila' },
    { value: '#8e44ad', label: 'Dunkellila' },
    { value: '#f1c40f', label: 'Gelb' },
  ];

  // State für Fehlerbehandlung
  const [saveError, setSaveError] = useState<string | null>(null);

  // Prüfen, ob Kategorien vorhanden sind
  const hasCategoriesAvailable = categories.length > 0;

  // Formular initialisieren
  const form = useForm<InvestmentFormValues>({
    resolver: zodResolver(investmentFormSchema),
    defaultValues: {
      name: investment?.name || "",
      // Verwende undefined anstelle von 0, wenn keine Kategorie verfügbar ist
      categoryId: investment?.categoryId || (hasCategoriesAvailable ? categories[0]?.id : undefined),
      // Konvertiere Cent-Werte in Euro für die Anzeige im Formular
      initialInvestment: investment?.initialInvestment ? investment.initialInvestment / 100 : 0,
      currentValue: investment?.currentValue ? investment.currentValue / 100 : 0,
      purchaseDate: investment?.purchaseDate ? new Date(investment.purchaseDate) : new Date(),
      notes: investment?.notes || "",
      color: investment?.color || "#1abc9c",
    },
  });

  // Formular absenden
  async function onSubmit(data: InvestmentFormValues) {
    try {
      // Zurücksetzen des Fehlers bei jedem Speicherversuch
      setSaveError(null);

      const currentDate = new Date().toISOString().split('T')[0];

      // Gemeinsame Felder für create und update
      const investmentData = {
        name: data.name,
        categoryId: data.categoryId,
        // Konvertiere Euro-Werte in Cent für die Speicherung in der Datenbank
        initialInvestment: Math.round(data.initialInvestment * 100),
        currentValue: Math.round(data.currentValue * 100),
        purchaseDate: formatDateForInput(data.purchaseDate),
        lastUpdateDate: currentDate,
        notes: data.notes,
        color: data.color || "#1abc9c",
      };

      let success = false;

      if (investment?.id) {
        // Investment aktualisieren
        success = await updateInvestment({
          ...investmentData,
          id: investment.id,
        });
      } else {
        // Neues Investment erstellen
        const newId = await createInvestment(investmentData);
        success = newId > 0;
      }

      if (success) {
        onSuccess();
      } else {
        throw new Error("Das Investment konnte nicht gespeichert werden.");
      }
    } catch (err) {
      console.error("Fehler beim Speichern des Investments:", err);
      setSaveError(err instanceof Error ? err.message : "Ein unbekannter Fehler ist aufgetreten beim Speichern des Investments.");
    }
  }

  return (
    <Form {...form}>
      {!hasCategoriesAvailable ? (
        <div className="bg-amber-50 border border-amber-200 p-6 rounded-lg shadow text-center mb-6">
          <p className="text-amber-600 font-medium">
            Bitte erstelle zuerst mindestens eine Kategorie vom Typ &quot;Investment&quot;, bevor du ein Investment anlegst.
          </p>
          <Button
            variant="outline"
            onClick={onCancel}
            className="mt-4"
          >
            Zurück
          </Button>
        </div>
      ) : (
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem className="col-span-1 md:col-span-2">
                <FormLabel>Name</FormLabel>
                <FormControl>
                  <Input placeholder="Name des Investments" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="categoryId"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Kategorie</FormLabel>
                <Select
                  onValueChange={(value) => field.onChange(parseInt(value))}
                  defaultValue={field.value !== undefined ? field.value.toString() : ''}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Kategorie auswählen" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {categories.map((category) => (
                      <SelectItem
                        key={category.id}
                        value={category.id?.toString() || ""}
                      >
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="purchaseDate"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Kaufdatum</FormLabel>
                <Popover>
                  <PopoverTrigger asChild>
                    <FormControl>
                      <Button
                        variant="outline"
                        className={cn(
                          "w-full pl-3 text-left font-normal",
                          !field.value && "text-muted-foreground"
                        )}
                      >
                        {field.value ? (
                          formatDateForInput(field.value)
                        ) : (
                          <span>Datum auswählen</span>
                        )}
                        <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                      </Button>
                    </FormControl>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={field.value}
                      onSelect={field.onChange}
                      disabled={(date) =>
                        date > new Date()
                      }
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="initialInvestment"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Investierter Betrag (in Euro)</FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    step="0.01"
                    placeholder="0.00"
                    {...field}
                  />
                </FormControl>
                <FormDescription>
                  Der ursprünglich investierte Betrag in Euro (z.B. 4000 für 4.000,00 €).
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="currentValue"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Aktueller Wert (in Euro)</FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    step="0.01"
                    placeholder="0.00"
                    {...field}
                  />
                </FormControl>
                <FormDescription>
                  Der aktuelle Wert des Investments in Euro (z.B. 4200 für 4.200,00 €).
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="notes"
            render={({ field }) => (
              <FormItem className="col-span-1 md:col-span-2">
                <FormLabel>Notizen (optional)</FormLabel>
                <FormControl>
                  <Input placeholder="Notizen zum Investment" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="color"
            render={({ field }) => (
              <FormItem className="col-span-1 md:col-span-2">
                <FormLabel>Farbe</FormLabel>
                <div className="grid grid-cols-3 gap-2">
                  {colorOptions.map((color) => (
                    <div
                      key={color.value}
                      className={cn(
                        "flex items-center justify-center p-2 rounded-md cursor-pointer border-2",
                        field.value === color.value
                          ? "border-black"
                          : "border-transparent"
                      )}
                      onClick={() => field.onChange(color.value)}
                    >
                      <div
                        className="w-full h-8 rounded-md"
                        style={{ backgroundColor: color.value }}
                        title={color.label}
                      />
                    </div>
                  ))}
                </div>
                <FormDescription>
                  Wähle eine Farbe für dein Investment
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {saveError && (
          <div className="bg-rose-50 border border-rose-200 p-4 rounded-md mb-4">
            <p className="text-rose-500 text-sm">{saveError}</p>
          </div>
        )}

        <div className="flex justify-end gap-2">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
          >
            Abbrechen
          </Button>
          <Button type="submit">
            {investment?.id ? "Aktualisieren" : "Erstellen"}
          </Button>
        </div>
      </form>
      )}
    </Form>
  );
}
