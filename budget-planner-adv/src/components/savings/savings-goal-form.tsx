"use client";

import { useState } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { AlertCircle } from "lucide-react";
import { CalendarIcon } from "lucide-react";
import { cn, formatDateForInput } from "@/lib/utils";
import { createSavingsGoal, updateSavingsGoal, type SavingsGoal } from "@/lib/db";

// Schema für die Formularvalidierung
const savingsGoalFormSchema = z.object({
  name: z.string().min(1, {
    message: "Der Name darf nicht leer sein.",
  }),
  targetAmount: z.coerce.number({
    required_error: "Bitte gib einen Zielbetrag ein.",
    invalid_type_error: "Bitte gib eine gültige Zahl ein.",
  }).positive({
    message: "Der Zielbetrag muss größer als 0 sein.",
  }),
  currentAmount: z.coerce.number({
    required_error: "Bitte gib einen aktuellen Betrag ein.",
    invalid_type_error: "Bitte gib eine gültige Zahl ein.",
  }).min(0, {
    message: "Der aktuelle Betrag darf nicht negativ sein.",
  }),
  targetDate: z.date().optional(),
  color: z.string().optional(),
});

type SavingsGoalFormValues = z.infer<typeof savingsGoalFormSchema>;

interface SavingsGoalFormProps {
  savingsGoal: SavingsGoal | null;
  onCancel: () => void;
  onSuccess: () => void;
  defaultValues?: SavingsGoal;
  mode?: 'create' | 'edit';
}

export function SavingsGoalForm({ savingsGoal, onCancel, onSuccess }: SavingsGoalFormProps) {
  // State für Fehlermeldungen
  const [error, setError] = useState<string | null>(null);

  // Vordefinierte Farben
  const colorOptions = [
    { value: '#e74c3c', label: 'Rot' },
    { value: '#e67e22', label: 'Orange' },
    { value: '#f1c40f', label: 'Gelb' },
    { value: '#2ecc71', label: 'Grün' },
    { value: '#1abc9c', label: 'Türkis' },
    { value: '#3498db', label: 'Blau' },
    { value: '#9b59b6', label: 'Lila' },
    { value: '#34495e', label: 'Dunkelgrau' },
    { value: '#7f8c8d', label: 'Grau' },
  ];
  // Formular initialisieren
  const form = useForm<SavingsGoalFormValues>({
    resolver: zodResolver(savingsGoalFormSchema),
    defaultValues: {
      name: savingsGoal?.name || "",
      // Konvertiere Cent-Werte in Euro für die Anzeige im Formular
      targetAmount: savingsGoal?.targetAmount ? savingsGoal.targetAmount / 100 : 0,
      currentAmount: savingsGoal?.currentAmount ? savingsGoal.currentAmount / 100 : 0,
      targetDate: (() => {
        if (!savingsGoal?.targetDate) return undefined;
        const date = new Date(savingsGoal.targetDate);
        return isNaN(date.getTime()) ? undefined : date;
      })(),
      color: savingsGoal?.color || "#3498db",
    },
  });

  // Formular absenden
  async function onSubmit(data: SavingsGoalFormValues) {
    try {
      // Fehler zurücksetzen
      setError(null);

      // Werte sind bereits durch Zod validiert
      // Umrechnung von Euro in Cent für die Speicherung
      const targetAmount = Math.round(Number(data.targetAmount) * 100);
      const currentAmount = Math.round(Number(data.currentAmount) * 100);

      if (savingsGoal?.id) {
        // Sparziel aktualisieren
        await updateSavingsGoal({
          id: savingsGoal.id,
          name: data.name.trim(),
          targetAmount: targetAmount,
          currentAmount: currentAmount,
          targetDate: data.targetDate ? formatDateForInput(data.targetDate) : undefined,
          color: data.color || "#3498db",
        });
      } else {
        // Neues Sparziel erstellen
        await createSavingsGoal({
          name: data.name.trim(),
          targetAmount: targetAmount,
          currentAmount: currentAmount,
          targetDate: data.targetDate ? formatDateForInput(data.targetDate) : undefined,
          color: data.color || "#3498db",
        });
      }

      onSuccess();
    } catch (error) {
      console.error("Fehler beim Speichern des Sparziels:", error);
      setError(error instanceof Error
        ? `Fehler beim Speichern des Sparziels: ${error.message}`
        : "Fehler beim Speichern des Sparziels. Bitte versuche es erneut.");
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        {error && (
          <div className="bg-destructive/15 text-destructive p-3 rounded-md flex items-start gap-2">
            <AlertCircle className="h-5 w-5 mt-0.5 flex-shrink-0" />
            <div>{error}</div>
          </div>
        )}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem className="col-span-1 md:col-span-2">
                <FormLabel>Name</FormLabel>
                <FormControl>
                  <Input placeholder="Name des Sparziels" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="targetAmount"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Zielbetrag</FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    step="0.01"
                    placeholder="0.00"
                    {...field}
                  />
                </FormControl>
                <FormDescription>
                  Der Gesamtbetrag, den du erreichen möchtest.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="currentAmount"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Aktueller Betrag</FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    step="0.01"
                    placeholder="0.00"
                    {...field}
                  />
                </FormControl>
                <FormDescription>
                  Der Betrag, den du bereits gespart hast.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="targetDate"
            render={({ field }) => (
              <FormItem className="col-span-1 md:col-span-2">
                <FormLabel>Zieldatum (optional)</FormLabel>
                <Popover>
                  <PopoverTrigger asChild>
                    <FormControl>
                      <Button
                        variant="outline"
                        className={cn(
                          "w-full pl-3 text-left font-normal",
                          !field.value && "text-muted-foreground"
                        )}
                      >
                        {field.value ? (
                          formatDateForInput(field.value)
                        ) : (
                          <span>Datum auswählen</span>
                        )}
                        <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                      </Button>
                    </FormControl>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={field.value}
                      onSelect={field.onChange}
                      disabled={(date) =>
                        date < new Date()
                      }
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
                <FormDescription>
                  Das Datum, bis zu dem du dein Sparziel erreichen möchtest.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="color"
            render={({ field }) => (
              <FormItem className="col-span-1 md:col-span-2">
                <FormLabel>Farbe</FormLabel>
                <div
                  className="grid grid-cols-3 gap-2"
                  role="radiogroup"
                  aria-labelledby="color-selection-label"
                >
                  <span id="color-selection-label" className="sr-only">Farbauswahl</span>
                  {colorOptions.map((color) => (
                    <div
                      key={color.value}
                      className={cn(
                        "flex items-center justify-center p-2 rounded-md cursor-pointer border-2",
                        field.value === color.value
                          ? "border-black"
                          : "border-transparent"
                      )}
                      onClick={() => field.onChange(color.value)}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter' || e.key === ' ') {
                          e.preventDefault();
                          field.onChange(color.value);
                        }
                      }}
                      tabIndex={0}
                      role="radio"
                      aria-checked={field.value === color.value}
                      aria-label={`Farbe ${color.label}`}
                    >
                      <div
                        className="w-full h-8 rounded-md"
                        style={{ backgroundColor: color.value }}
                        title={color.label}
                      />
                    </div>
                  ))}
                </div>
                <FormDescription>
                  Wähle eine Farbe für dein Sparziel
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="flex justify-end gap-2">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
          >
            Abbrechen
          </Button>
          <Button type="submit">
            {savingsGoal?.id ? "Aktualisieren" : "Erstellen"}
          </Button>
        </div>
      </form>
    </Form>
  );
}
