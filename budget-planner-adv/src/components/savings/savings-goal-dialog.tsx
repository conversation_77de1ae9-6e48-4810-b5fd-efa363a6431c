'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { SavingsGoalForm } from './savings-goal-form';
import { PlusIcon, PencilIcon } from 'lucide-react';
import { SavingsGoal } from '@/lib/db';

interface SavingsGoalDialogProps {
  onSuccess?: () => void;
  savingsGoal?: SavingsGoal;
  mode?: 'create' | 'edit';
  trigger?: React.ReactNode;
}

export function SavingsGoalDialog({ 
  onSuccess, 
  savingsGoal, 
  mode = 'create',
  trigger 
}: SavingsGoalDialogProps) {
  const [open, setOpen] = useState(false);

  const handleSuccess = () => {
    setOpen(false);
    if (onSuccess) {
      onSuccess();
    }
  };

  const isEditMode = mode === 'edit';

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button>
            {isEditMode ? (
              <PencilIcon className="mr-2 h-4 w-4" />
            ) : (
              <PlusIcon className="mr-2 h-4 w-4" />
            )}
            {isEditMode ? 'Bearbeiten' : 'Neues Sparziel'}
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>
            {isEditMode ? 'Sparziel bearbeiten' : 'Neues Sparziel'}
          </DialogTitle>
          <DialogDescription>
            {isEditMode 
              ? 'Bearbeite dein Sparziel und verfolge deinen Fortschritt.' 
              : 'Erstelle ein neues Sparziel und verfolge deinen Fortschritt.'}
          </DialogDescription>
        </DialogHeader>
        <SavingsGoalForm
          savingsGoal={savingsGoal || null}
          onSuccess={handleSuccess}
          onCancel={() => setOpen(false)}
          defaultValues={savingsGoal}
          mode={mode}
        />
      </DialogContent>
    </Dialog>
  );
}
