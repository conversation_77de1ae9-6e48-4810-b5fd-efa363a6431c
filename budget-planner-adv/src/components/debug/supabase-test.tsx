"use client";

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { supabase, getDeviceId } from '@/lib/supabase/client';
import { SavingsGoalService } from '@/lib/storage/services';

export function SupabaseTest() {
  const [result, setResult] = useState<string>('');
  const [loading, setLoading] = useState(false);

  const testConnection = async () => {
    setLoading(true);
    setResult('Testing connection...');
    
    try {
      // Test 1: Einfache Verbindung
      const { data, error } = await supabase
        .from('categories')
        .select('count', { count: 'exact', head: true });
      
      if (error) {
        setResult(`Connection Error: ${JSON.stringify(error, null, 2)}`);
        return;
      }
      
      setResult(`✅ Connection successful! Categories count: ${data || 0}`);
      
    } catch (error) {
      setResult(`❌ Connection failed: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const testInsert = async () => {
    setLoading(true);
    setResult('Testing insert...');
    
    try {
      const deviceId = getDeviceId();
      const testCategory = {
        name: 'Test Category',
        type: 'expense',
        color: '#FF0000',
        device_id: deviceId,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
      
      const { data, error } = await supabase
        .from('categories')
        .insert([testCategory])
        .select();
      
      if (error) {
        setResult(`Insert Error: ${JSON.stringify(error, null, 2)}`);
        return;
      }
      
      setResult(`✅ Insert successful! Data: ${JSON.stringify(data, null, 2)}`);
      
    } catch (error) {
      setResult(`❌ Insert failed: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const testSelect = async () => {
    setLoading(true);
    setResult('Testing select...');
    
    try {
      const { data, error } = await supabase
        .from('categories')
        .select('*')
        .limit(5);
      
      if (error) {
        setResult(`Select Error: ${JSON.stringify(error, null, 2)}`);
        return;
      }
      
      setResult(`✅ Select successful! Data: ${JSON.stringify(data, null, 2)}`);
      
    } catch (error) {
      setResult(`❌ Select failed: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const testInvestmentInsert = async () => {
    setLoading(true);
    setResult('Testing investment insert...');

    try {
      const deviceId = getDeviceId();

      // Erst eine Test-Kategorie für Investment erstellen
      const testCategory = {
        name: 'Test Investment Category',
        type: 'investment',
        color: '#8B5CF6',
        device_id: deviceId,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      const { data: categoryData, error: categoryError } = await supabase
        .from('categories')
        .insert([testCategory])
        .select();

      if (categoryError) {
        setResult(`Category Insert Error: ${JSON.stringify(categoryError, null, 2)}`);
        return;
      }

      const categoryId = categoryData[0].id;

      // Jetzt Investment erstellen
      const testInvestment = {
        name: 'Test Investment',
        category_id: categoryId,
        initial_investment: 1000.00,
        current_value: 1100.00,
        purchase_date: '2024-01-01',
        last_update_date: '2024-01-15',
        notes: 'Test investment for debugging',
        color: '#8B5CF6',
        device_id: deviceId,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      const { data: investmentData, error: investmentError } = await supabase
        .from('investments')
        .insert([testInvestment])
        .select();

      if (investmentError) {
        setResult(`Investment Insert Error: ${JSON.stringify(investmentError, null, 2)}`);
        return;
      }

      setResult(`✅ Investment insert successful! Data: ${JSON.stringify(investmentData, null, 2)}`);

    } catch (error) {
      setResult(`❌ Investment insert failed: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const testTransactionInsert = async () => {
    setLoading(true);
    setResult('Testing transaction insert...');

    try {
      const deviceId = getDeviceId();

      // Erst eine Test-Kategorie für Transaction erstellen
      const testCategory = {
        name: 'Test Transaction Category',
        type: 'expense',
        color: '#EF4444',
        device_id: deviceId,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      const { data: categoryData, error: categoryError } = await supabase
        .from('categories')
        .insert([testCategory])
        .select();

      if (categoryError) {
        setResult(`Category Insert Error: ${JSON.stringify(categoryError, null, 2)}`);
        return;
      }

      const categoryId = categoryData[0].id;

      // Jetzt Transaction erstellen
      const testTransaction = {
        description: 'Test Transaction',
        amount: 50.00,
        date: '2024-01-15',
        category_id: categoryId,
        type: 'expense',
        notes: 'Test transaction for debugging',
        device_id: deviceId,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      const { data: transactionData, error: transactionError } = await supabase
        .from('transactions')
        .insert([testTransaction])
        .select();

      if (transactionError) {
        setResult(`Transaction Insert Error: ${JSON.stringify(transactionError, null, 2)}`);
        return;
      }

      setResult(`✅ Transaction insert successful! Data: ${JSON.stringify(transactionData, null, 2)}`);

    } catch (error) {
      setResult(`❌ Transaction insert failed: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const clearAllSupabaseData = async () => {
    setLoading(true);
    setResult('⚠️ CLEARING ALL SUPABASE DATA...');

    try {
      // Lösche alle Daten in der richtigen Reihenfolge (wegen Foreign Keys)
      await supabase.from('transactions').delete().neq('id', 0);
      await supabase.from('investments').delete().neq('id', 0);
      await supabase.from('savings_goals').delete().neq('id', 0);
      await supabase.from('account_balances').delete().neq('id', 0);
      await supabase.from('categories').delete().neq('id', 0);
      await supabase.from('settings').delete().neq('id', 0);

      setResult(`✅ ALL SUPABASE DATA CLEARED! Database is now empty.`);

    } catch (error) {
      setResult(`❌ Clear failed: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const syncPushOnly = async () => {
    setLoading(true);
    setResult('⬆️ PUSH-ONLY SYNC (LOKAL → SUPABASE)...');

    try {
      const { hybridStorageAdapter } = await import('@/lib/storage/hybrid-adapter');

      await hybridStorageAdapter.sync({
        direction: 'push',
        forceSync: true
      });

      setResult(`✅ PUSH-ONLY SYNC ABGESCHLOSSEN!

Das bedeutet:
- ✅ Alle LOKALEN Daten wurden zu Supabase gepusht
- ✅ Deine Löschungen sind jetzt auch in Supabase
- ✅ Supabase wurde mit deinen lokalen Daten überschrieben

Ergebnis: Lokale Daten gewinnen!`);

    } catch (error) {
      setResult(`❌ Push-Only Sync failed: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const syncPullOnly = async () => {
    setLoading(true);
    setResult('⬇️ PULL-ONLY SYNC (SUPABASE → LOKAL)...');

    try {
      const { hybridStorageAdapter } = await import('@/lib/storage/hybrid-adapter');

      await hybridStorageAdapter.sync({
        direction: 'pull',
        forceSync: true
      });

      setResult(`✅ PULL-ONLY SYNC ABGESCHLOSSEN!

Das bedeutet:
- ✅ Alle SUPABASE Daten wurden zu LocalStorage gepullt
- ✅ Deine lokalen Änderungen wurden überschrieben
- ✅ LocalStorage wurde mit Supabase-Daten überschrieben

Ergebnis: Supabase Daten gewinnen!`);

    } catch (error) {
      setResult(`❌ Pull-Only Sync failed: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const fixSupabaseRLS = async () => {
    setLoading(true);
    setResult('🔧 REPARIERE SUPABASE RLS...');

    try {
      // Teste verschiedene Supabase-Operationen
      const tests = [];

      // Test 1: Select Categories
      try {
        const { data, error } = await supabase.from('categories').select('*').limit(1);
        tests.push(`✅ SELECT categories: ${data?.length || 0} Einträge`);
      } catch (error) {
        tests.push(`❌ SELECT categories: ${error}`);
      }

      // Test 2: Delete Categories (mit device_id)
      try {
        const deviceId = getDeviceId();
        const { error } = await supabase
          .from('categories')
          .delete()
          .eq('device_id', deviceId);
        tests.push(`✅ DELETE categories (device_id): ${error ? error.message : 'Erfolgreich'}`);
      } catch (error) {
        tests.push(`❌ DELETE categories: ${error}`);
      }

      // Test 3: Insert Category
      try {
        const deviceId = getDeviceId();
        const { error } = await supabase
          .from('categories')
          .insert({
            id: 99999,
            name: 'Test Category',
            type: 'expense',
            color: '#ff0000',
            device_id: deviceId,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          });
        tests.push(`✅ INSERT category: ${error ? error.message : 'Erfolgreich'}`);
      } catch (error) {
        tests.push(`❌ INSERT category: ${error}`);
      }

      // Test 4: Delete Test Category
      try {
        const { error } = await supabase
          .from('categories')
          .delete()
          .eq('id', 99999);
        tests.push(`✅ DELETE test category: ${error ? error.message : 'Erfolgreich'}`);
      } catch (error) {
        tests.push(`❌ DELETE test category: ${error}`);
      }

      setResult(`🔧 SUPABASE RLS TESTS:

DEVICE ID: ${getDeviceId()}

TESTS:
${tests.join('\n')}

EMPFEHLUNG:
- Wenn DELETE/INSERT fehlschlägt: RLS-Policies prüfen
- Wenn nur SELECT funktioniert: Schreibrechte fehlen
- 409 Conflict = RLS blockiert Operation`);

    } catch (error) {
      setResult(`❌ RLS Test failed: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const debugAccountBalances = async () => {
    setLoading(true);
    setResult('🔍 DEBUGGING ACCOUNT BALANCES...');

    try {
      // 1. Hole Account Balances aus LocalStorage
      const localBalances = JSON.parse(localStorage.getItem('account_balances') || '[]');

      // 2. Hole Account Balances aus Supabase
      const { data: supabaseBalances, error: supabaseError } = await supabase
        .from('account_balances')
        .select('*');

      if (supabaseError) {
        setResult(`❌ Supabase Account Balances Error: ${JSON.stringify(supabaseError, null, 2)}`);
        return;
      }

      setResult(`🔍 ACCOUNT BALANCES DEBUG:

LOCALSTORAGE ACCOUNT BALANCES (${localBalances.length}):
${localBalances.map((b: any) => `  ${b.name} (${b.type}): ${b.balance}€ - ${b.month}/${b.year}`).join('\n') || 'Keine'}

SUPABASE ACCOUNT BALANCES (${supabaseBalances?.length || 0}):
${supabaseBalances?.map(b => `  ${b.name} (${b.type}): ${b.balance}€ - ${b.month}/${b.year}`).join('\n') || 'Keine'}

DETAILS LOCALSTORAGE:
${localBalances.map((b: any, i: number) => `${i + 1}. ${JSON.stringify(b, null, 2)}`).join('\n\n') || 'Keine Daten'}

PROBLEM: ${localBalances.length > (supabaseBalances?.length || 0) ? 'Nicht alle LocalStorage Balances in Supabase!' : 'Alle Balances synchronisiert.'}`);

    } catch (error) {
      setResult(`❌ Debug failed: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const cleanOrphanedData = async () => {
    setLoading(true);
    setResult('🧹 BEREINIGE VERWAISTE DATEN...');

    try {
      // 1. Hole aktuelle Categories aus LocalStorage
      const localCategories = JSON.parse(localStorage.getItem('categories') || '[]');
      const validCategoryIds = localCategories.map((c: any) => c.id);

      // 2. Hole Transactions und filtere verwaiste
      const localTransactions = JSON.parse(localStorage.getItem('transactions') || '[]');
      const orphanedTransactions = localTransactions.filter((t: any) =>
        t.categoryId && !validCategoryIds.includes(t.categoryId)
      );
      const validTransactions = localTransactions.filter((t: any) =>
        !t.categoryId || validCategoryIds.includes(t.categoryId)
      );

      // 3. Hole Investments und filtere verwaiste
      const localInvestments = JSON.parse(localStorage.getItem('investments') || '[]');
      const orphanedInvestments = localInvestments.filter((i: any) =>
        i.categoryId && !validCategoryIds.includes(i.categoryId)
      );
      const validInvestments = localInvestments.filter((i: any) =>
        !i.categoryId || validCategoryIds.includes(i.categoryId)
      );

      // 4. Speichere bereinigte Daten
      localStorage.setItem('transactions', JSON.stringify(validTransactions));
      localStorage.setItem('investments', JSON.stringify(validInvestments));

      const removedTransactions = localTransactions.length - validTransactions.length;
      const removedInvestments = localInvestments.length - validInvestments.length;

      setResult(`✅ VERWAISTE DATEN BEREINIGT!

VORHER:
- Categories: ${localCategories.length}
- Transactions: ${localTransactions.length}
- Investments: ${localInvestments.length}

VERWAISTE DATEN GEFUNDEN:
${orphanedTransactions.map((t: any) => `- Transaction: "${t.description}" (Category-ID: ${t.categoryId})`).join('\n')}
${orphanedInvestments.map((i: any) => `- Investment: "${i.name}" (Category-ID: ${i.categoryId})`).join('\n')}

NACHHER:
- Categories: ${localCategories.length} (unverändert)
- Transactions: ${validTransactions.length} (${removedTransactions} entfernt)
- Investments: ${validInvestments.length} (${removedInvestments} entfernt)

✅ LocalStorage ist jetzt bereinigt!`);

    } catch (error) {
      setResult(`❌ Cleanup failed: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const debugCategoryIds = async () => {
    setLoading(true);
    setResult('🔍 DEBUGGING CATEGORY IDs...');

    try {
      // 1. Hole alle Categories aus Supabase
      const { data: supabaseCategories, error: supabaseError } = await supabase
        .from('categories')
        .select('*');

      if (supabaseError) {
        setResult(`❌ Supabase Categories Error: ${JSON.stringify(supabaseError, null, 2)}`);
        return;
      }

      // 2. Hole alle LocalStorage Daten
      const localCategories = JSON.parse(localStorage.getItem('categories') || '[]');
      const localTransactions = JSON.parse(localStorage.getItem('transactions') || '[]');
      const localInvestments = JSON.parse(localStorage.getItem('investments') || '[]');

      // 3. Sammle alle verwendeten Category-IDs
      const usedCategoryIds = new Set();
      localTransactions.forEach((t: any) => {
        if (t.categoryId) usedCategoryIds.add(t.categoryId);
      });
      localInvestments.forEach((i: any) => {
        if (i.categoryId) usedCategoryIds.add(i.categoryId);
      });

      // 4. Vergleiche
      const localCategoryIds = localCategories.map((c: any) => c.id);
      const supabaseCategoryIds = supabaseCategories?.map(c => c.id) || [];
      const missingIds = Array.from(usedCategoryIds).filter(id => !supabaseCategoryIds.includes(id));
      const orphanedIds = Array.from(usedCategoryIds).filter(id => !localCategoryIds.includes(id));

      setResult(`🔍 CATEGORY ID DEBUG:

LOCALSTORAGE CATEGORIES (${localCategories.length}):
${localCategories.map((c: any) => `  ${c.id}: ${c.name} (${c.type})`).join('\n') || 'Keine'}

SUPABASE CATEGORIES (${supabaseCategories?.length || 0}):
${supabaseCategories?.map(c => `  ${c.id}: ${c.name} (${c.type})`).join('\n') || 'Keine'}

VERWENDETE CATEGORY IDs:
Transactions: ${localTransactions.map((t: any) => t.categoryId).filter(Boolean).join(', ') || 'Keine'}
Investments: ${localInvestments.map((i: any) => i.categoryId).filter(Boolean).join(', ') || 'Keine'}

❌ FEHLENDE IDs IN SUPABASE: ${missingIds.length > 0 ? missingIds.join(', ') : 'Keine'}
🗑️ VERWAISTE IDs (nicht in LocalStorage Categories): ${orphanedIds.length > 0 ? orphanedIds.join(', ') : 'Keine'}

PROBLEM: ${orphanedIds.length > 0 ? 'Verwaiste Daten in LocalStorage!' : missingIds.length > 0 ? 'Category-IDs fehlen in Supabase!' : 'Alle IDs sind korrekt!'}`);

    } catch (error) {
      setResult(`❌ Debug failed: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const clearAllSavingsGoals = async () => {
    setLoading(true);
    setResult('🗑️ LÖSCHE ALLE SAVINGS GOALS (SUPABASE + LOKAL)...');

    try {
      // 1. Lösche ALLE Savings Goals aus Supabase
      const { error: supabaseError } = await supabase
        .from('savings_goals')
        .delete()
        .neq('id', 0); // Lösche alle Einträge

      if (supabaseError) {
        setResult(`❌ Supabase Clear failed: ${JSON.stringify(supabaseError, null, 2)}`);
        return;
      }

      // 2. Lösche ALLE Savings Goals aus LocalStorage
      const allSavingsGoals = await SavingsGoalService.getSavingsGoals();
      for (const goal of allSavingsGoals) {
        if (goal.id) {
          await SavingsGoalService.deleteSavingsGoal(goal.id);
        }
      }

      setResult(`✅ ALLE SAVINGS GOALS GELÖSCHT!\n- Supabase: ✅ Gelöscht\n- LocalStorage: ✅ Gelöscht`);

    } catch (error) {
      setResult(`❌ Clear failed: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const clearTestData = async () => {
    setLoading(true);
    setResult('Clearing test data...');

    try {
      // Lösche Test-Transactions
      await supabase
        .from('transactions')
        .delete()
        .eq('description', 'Test Transaction');

      // Lösche Test-Investments
      await supabase
        .from('investments')
        .delete()
        .eq('name', 'Test Investment');

      // Lösche Test-Kategorien
      await supabase
        .from('categories')
        .delete()
        .in('name', ['Test Category', 'Test Investment Category', 'Test Transaction Category']);

      setResult(`✅ Test data cleared!`);

    } catch (error) {
      setResult(`❌ Clear failed: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-4xl">
      <CardHeader>
        <CardTitle>Supabase Connection Test</CardTitle>
        <CardDescription>
          Test the Supabase connection and basic operations
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex gap-2 flex-wrap">
          <Button onClick={testConnection} disabled={loading}>
            Test Connection
          </Button>
          <Button onClick={testInsert} disabled={loading}>
            Test Insert
          </Button>
          <Button onClick={testInvestmentInsert} disabled={loading}>
            Test Investment
          </Button>
          <Button onClick={testTransactionInsert} disabled={loading}>
            Test Transaction
          </Button>
          <Button onClick={testSelect} disabled={loading}>
            Test Select
          </Button>
          <Button onClick={debugCategoryIds} disabled={loading} variant="secondary">
            🔍 Debug Category IDs
          </Button>
          <Button onClick={cleanOrphanedData} disabled={loading} variant="outline">
            🧹 Bereinige verwaiste Daten
          </Button>
          <Button onClick={debugAccountBalances} disabled={loading} variant="secondary">
            🏦 Debug Account Balances
          </Button>
          <Button onClick={fixSupabaseRLS} disabled={loading} variant="destructive">
            🔧 Test Supabase RLS
          </Button>
          <Button onClick={syncPushOnly} disabled={loading} variant="outline">
            ⬆️ Push-Only Sync
          </Button>
          <Button onClick={syncPullOnly} disabled={loading} variant="outline">
            ⬇️ Pull-Only Sync
          </Button>
          <Button onClick={clearTestData} disabled={loading} variant="outline">
            Clear Test Data
          </Button>
          <Button onClick={clearAllSavingsGoals} disabled={loading} variant="destructive">
            🗑️ DELETE ALL SAVINGS GOALS
          </Button>

        </div>
        
        <div className="mt-4">
          <h4 className="text-sm font-medium mb-2">Result:</h4>
          <pre className="bg-muted p-4 rounded text-xs overflow-auto max-h-96">
            {result || 'No test run yet...'}
          </pre>
        </div>
        
        <div className="text-xs text-muted-foreground">
          <p>Device ID: {getDeviceId()}</p>
          <p>Supabase URL: {process.env.NEXT_PUBLIC_SUPABASE_URL}</p>
          <p>Sync Enabled: {process.env.NEXT_PUBLIC_SYNC_ENABLED}</p>
        </div>
      </CardContent>
    </Card>
  );
}
