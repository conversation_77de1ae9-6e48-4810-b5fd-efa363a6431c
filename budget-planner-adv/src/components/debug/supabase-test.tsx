"use client";

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { supabase, getDeviceId } from '@/lib/supabase/client';

export function SupabaseTest() {
  const [result, setResult] = useState<string>('');
  const [loading, setLoading] = useState(false);

  const testConnection = async () => {
    setLoading(true);
    setResult('Testing connection...');
    
    try {
      // Test 1: Einfache Verbindung
      const { data, error } = await supabase
        .from('categories')
        .select('count', { count: 'exact', head: true });
      
      if (error) {
        setResult(`Connection Error: ${JSON.stringify(error, null, 2)}`);
        return;
      }
      
      setResult(`✅ Connection successful! Categories count: ${data || 0}`);
      
    } catch (error) {
      setResult(`❌ Connection failed: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const testInsert = async () => {
    setLoading(true);
    setResult('Testing insert...');
    
    try {
      const deviceId = getDeviceId();
      const testCategory = {
        name: 'Test Category',
        type: 'expense',
        color: '#FF0000',
        device_id: deviceId,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
      
      const { data, error } = await supabase
        .from('categories')
        .insert([testCategory])
        .select();
      
      if (error) {
        setResult(`Insert Error: ${JSON.stringify(error, null, 2)}`);
        return;
      }
      
      setResult(`✅ Insert successful! Data: ${JSON.stringify(data, null, 2)}`);
      
    } catch (error) {
      setResult(`❌ Insert failed: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const testSelect = async () => {
    setLoading(true);
    setResult('Testing select...');
    
    try {
      const { data, error } = await supabase
        .from('categories')
        .select('*')
        .limit(5);
      
      if (error) {
        setResult(`Select Error: ${JSON.stringify(error, null, 2)}`);
        return;
      }
      
      setResult(`✅ Select successful! Data: ${JSON.stringify(data, null, 2)}`);
      
    } catch (error) {
      setResult(`❌ Select failed: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const clearTestData = async () => {
    setLoading(true);
    setResult('Clearing test data...');
    
    try {
      const { error } = await supabase
        .from('categories')
        .delete()
        .eq('name', 'Test Category');
      
      if (error) {
        setResult(`Clear Error: ${JSON.stringify(error, null, 2)}`);
        return;
      }
      
      setResult(`✅ Test data cleared!`);
      
    } catch (error) {
      setResult(`❌ Clear failed: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-4xl">
      <CardHeader>
        <CardTitle>Supabase Connection Test</CardTitle>
        <CardDescription>
          Test the Supabase connection and basic operations
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex gap-2 flex-wrap">
          <Button onClick={testConnection} disabled={loading}>
            Test Connection
          </Button>
          <Button onClick={testInsert} disabled={loading}>
            Test Insert
          </Button>
          <Button onClick={testSelect} disabled={loading}>
            Test Select
          </Button>
          <Button onClick={clearTestData} disabled={loading} variant="outline">
            Clear Test Data
          </Button>
        </div>
        
        <div className="mt-4">
          <h4 className="text-sm font-medium mb-2">Result:</h4>
          <pre className="bg-muted p-4 rounded text-xs overflow-auto max-h-96">
            {result || 'No test run yet...'}
          </pre>
        </div>
        
        <div className="text-xs text-muted-foreground">
          <p>Device ID: {getDeviceId()}</p>
          <p>Supabase URL: {process.env.NEXT_PUBLIC_SUPABASE_URL}</p>
          <p>Sync Enabled: {process.env.NEXT_PUBLIC_SYNC_ENABLED}</p>
        </div>
      </CardContent>
    </Card>
  );
}
