"use client";

import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card";
import { getComparisonReport, type ComparisonReportData } from "@/lib/reports-db";
import { getCategories, type Category } from "@/lib/db";
import { formatCurrency } from "@/lib/utils";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
} from 'chart.js';
import { Bar } from 'react-chartjs-2';

// Chart.js registrieren
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
);

interface ComparisonReportProps {
  type: string;
  period1: {
    month: number;
    year: number;
  };
  period2: {
    month: number;
    year: number;
  };
}

export function ComparisonReport({ type, period1, period2 }: ComparisonReportProps) {
  const [loading, setLoading] = useState(true);
  const [reportData, setReportData] = useState<ComparisonReportData | null>(null);
  const [categories, setCategories] = useState<Category[]>([]);

  useEffect(() => {
    async function loadData() {
      try {
        setLoading(true);

        // Lade Berichtsdaten
        const report = await getComparisonReport(type as "month-to-month" | "year-to-year", period1, period2);
        setReportData(report);

        // Lade Kategorien
        const categoriesData = await getCategories();
        setCategories(categoriesData);
      } catch (error) {
        console.error("Fehler beim Laden der Berichtsdaten:", error);

        // Keine Fallback-Daten mehr, nur leere Daten
        setReportData({
          period1: {
            label: type === "month-to-month" ? `${getMonthName(period1.month)} ${period1.year}` : period1.year.toString(),
            income: 0,
            expenses: 0,
            savings: 0,
            savingsRate: 0
          },
          period2: {
            label: type === "month-to-month" ? `${getMonthName(period2.month)} ${period2.year}` : period2.year.toString(),
            income: 0,
            expenses: 0,
            savings: 0,
            savingsRate: 0
          },
          differences: {
            income: 0,
            incomePercentage: 0,
            expenses: 0,
            expensesPercentage: 0,
            savings: 0,
            savingsPercentage: 0
          },
          categoryComparison: []
        });

        // Leere Kategorien
        setCategories([]);
      } finally {
        setLoading(false);
      }
    }

    loadData();
  }, [type, period1, period2]);

  function getCategoryName(categoryId: number): string {
    const category = categories.find(c => c.id === categoryId);
    return category ? category.name : "Unbekannt";
  }

  function getCategoryColor(categoryId: number): string {
    const category = categories.find(c => c.id === categoryId);
    return category ? category.color : "#cccccc";
  }

  function getMonthName(monthNumber: number): string {
    const months = [
      "Januar", "Februar", "März", "April", "Mai", "Juni",
      "Juli", "August", "September", "Oktober", "November", "Dezember"
    ];
    return months[monthNumber - 1];
  }

  function getDifferenceClass(value: number): string {
    return value >= 0 ? "text-emerald-500" : "text-rose-500";
  }

  // Spezielle Funktion für Ausgabenkategorien, bei denen negative Werte positiv sind (weniger Ausgaben)
  function getExpenseDifferenceClass(value: number): string {
    return value <= 0 ? "text-emerald-500" : "text-rose-500";
  }

  function formatPercentage(value: number): string {
    const prefix = value >= 0 ? "+" : "";
    return `${prefix}${value.toFixed(1)}%`;
  }

  if (loading) {
    return <div className="flex justify-center p-8">Lade Berichtsdaten...</div>;
  }

  if (!reportData) {
    return <div className="flex justify-center p-8">Keine Daten verfügbar</div>;
  }

  return (
    <div className="space-y-6">
      <div className="text-center mb-6">
        <h2 className="text-2xl font-bold">
          Vergleich: {reportData.period1.label} vs. {reportData.period2.label}
        </h2>
      </div>

      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Einnahmen</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex justify-between items-end">
              <div>
                <div className="text-sm text-slate-500">{reportData.period1.label}</div>
                <div className="text-lg font-bold text-emerald-500">
                  {formatCurrency(reportData.period1.income)}
                </div>
              </div>
              <div className="text-right">
                <div className="text-sm text-slate-500">{reportData.period2.label}</div>
                <div className="text-lg font-bold text-emerald-500">
                  {formatCurrency(reportData.period2.income)}
                </div>
              </div>
            </div>
            <div className="mt-2 pt-2 border-t">
              <div className="flex justify-between items-center">
                <div className="text-sm">Differenz:</div>
                <div className={`text-sm font-bold ${getDifferenceClass(reportData.differences.income)}`}>
                  {formatCurrency(reportData.differences.income, true)} ({formatPercentage(reportData.differences.incomePercentage)})
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Ausgaben</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex justify-between items-end">
              <div>
                <div className="text-sm text-slate-500">{reportData.period1.label}</div>
                <div className="text-lg font-bold text-rose-500">
                  {formatCurrency(reportData.period1.expenses, true)}
                </div>
              </div>
              <div className="text-right">
                <div className="text-sm text-slate-500">{reportData.period2.label}</div>
                <div className="text-lg font-bold text-rose-500">
                  {formatCurrency(reportData.period2.expenses, true)}
                </div>
              </div>
            </div>
            <div className="mt-2 pt-2 border-t">
              <div className="flex justify-between items-center">
                <div className="text-sm">Differenz:</div>
                <div className={`text-sm font-bold ${getExpenseDifferenceClass(reportData.differences.expenses)}`}>
                  {formatCurrency(reportData.differences.expenses, true)} ({formatPercentage(reportData.differences.expensesPercentage)})
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Ersparnisse</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex justify-between items-end">
              <div>
                <div className="text-sm text-slate-500">{reportData.period1.label}</div>
                <div className="text-lg font-bold text-blue-500">
                  {formatCurrency(reportData.period1.savings, true)}
                </div>
                <div className="text-xs text-slate-500">
                  Sparrate: {reportData.period1.savingsRate.toFixed(1)}%
                </div>
              </div>
              <div className="text-right">
                <div className="text-sm text-slate-500">{reportData.period2.label}</div>
                <div className="text-lg font-bold text-blue-500">
                  {formatCurrency(reportData.period2.savings, true)}
                </div>
                <div className="text-xs text-slate-500">
                  Sparrate: {reportData.period2.savingsRate.toFixed(1)}%
                </div>
              </div>
            </div>
            <div className="mt-2 pt-2 border-t">
              <div className="flex justify-between items-center">
                <div className="text-sm">Differenz:</div>
                <div className={`text-sm font-bold ${getDifferenceClass(reportData.differences.savings)}`}>
                  {formatCurrency(reportData.differences.savings, true)} ({formatPercentage(reportData.differences.savingsPercentage)})
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Kategorien-Vergleich</CardTitle>
          <CardDescription>
            Vergleich der Ausgaben nach Kategorien
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-2">Kategorie</th>
                  <th className="text-right py-2">{reportData.period1.label}</th>
                  <th className="text-right py-2">{reportData.period2.label}</th>
                  <th className="text-right py-2">Differenz</th>
                  <th className="text-right py-2">Änderung</th>
                </tr>
              </thead>
              <tbody>
                {reportData.categoryComparison.map((category) => (
                  <tr key={category.categoryId} className="border-b">
                    <td className="py-2">
                      <div className="flex items-center gap-2">
                        <div
                          className="w-3 h-3 rounded-full"
                          style={{ backgroundColor: getCategoryColor(category.categoryId) }}
                        />
                        {getCategoryName(category.categoryId)}
                      </div>
                    </td>
                    <td className="text-right py-2">
                      {formatCurrency(category.period1Amount, true)}
                    </td>
                    <td className="text-right py-2">
                      {formatCurrency(category.period2Amount, true)}
                    </td>
                    <td className="text-right py-2">
                      {formatCurrency(category.difference, true)}
                    </td>
                    <td className={`text-right py-2 ${getExpenseDifferenceClass(category.differencePercentage)}`}>
                      {formatPercentage(category.differencePercentage)}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Visualisierung</CardTitle>
          <CardDescription>
            Grafische Darstellung des Vergleichs
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-80">
            {loading ? (
              <div className="h-full flex items-center justify-center">
                <p className="text-slate-500">Lade Daten...</p>
              </div>
            ) : reportData ? (
              <Bar
                data={{
                  labels: ['Einnahmen', 'Ausgaben', 'Ersparnisse'],
                  datasets: [
                    {
                      label: reportData.period1.label,
                      data: [
                        reportData.period1.income / 100, // Umrechnung von Cent in Euro
                        reportData.period1.expenses / 100,
                        reportData.period1.savings / 100
                      ],
                      backgroundColor: 'rgba(53, 162, 235, 0.5)',
                      borderColor: 'rgba(53, 162, 235, 1)',
                      borderWidth: 1
                    },
                    {
                      label: reportData.period2.label,
                      data: [
                        reportData.period2.income / 100, // Umrechnung von Cent in Euro
                        reportData.period2.expenses / 100,
                        reportData.period2.savings / 100
                      ],
                      backgroundColor: 'rgba(255, 99, 132, 0.5)',
                      borderColor: 'rgba(255, 99, 132, 1)',
                      borderWidth: 1
                    }
                  ]
                }}
                options={{
                  responsive: true,
                  maintainAspectRatio: false,
                  scales: {
                    y: {
                      beginAtZero: true,
                      ticks: {
                        callback: (value) => formatCurrency(value as number, false)
                      }
                    }
                  },
                  plugins: {
                    legend: {
                      position: 'top',
                    },
                    tooltip: {
                      callbacks: {
                        label: (context) => {
                          const value = context.raw as number;
                          return `${context.dataset.label}: ${formatCurrency(value, false)}`;
                        }
                      }
                    }
                  }
                }}
              />
            ) : (
              <div className="h-full flex flex-col items-center justify-center gap-4">
                <p className="text-slate-500 text-center">Keine Daten verfügbar</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
