"use client";

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { 
  Cloud, 
  CloudOff, 
  RefreshCw, 
  CheckCircle, 
  AlertCircle, 
  Wifi, 
  WifiOff,
  Database,
  Smartphone
} from 'lucide-react';
import { syncService, SyncResult } from '@/lib/supabase';
import { formatDistanceToNow } from 'date-fns';
import { de } from 'date-fns/locale';

interface SyncStatusProps {
  showDetails?: boolean;
  compact?: boolean;
}

export function SyncStatus({ showDetails = false, compact = false }: SyncStatusProps) {
  const [status, setStatus] = useState(syncService.getStatus());
  const [lastSyncResult, setLastSyncResult] = useState<SyncResult | null>(null);
  const [isManualSyncing, setIsManualSyncing] = useState(false);
  const [connectionTest, setConnectionTest] = useState<{ localStorage: boolean; supabase: boolean } | null>(null);

  // Status regelmäßig aktualisieren
  useEffect(() => {
    const interval = setInterval(() => {
      setStatus(syncService.getStatus());
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  // Verbindungstest beim Mount
  useEffect(() => {
    testConnection();
  }, []);

  const testConnection = async () => {
    try {
      const result = await syncService.testConnection();
      setConnectionTest(result);
    } catch (error) {
      console.error('Verbindungstest fehlgeschlagen:', error);
      setConnectionTest({ localStorage: true, supabase: false });
    }
  };

  const handleManualSync = async () => {
    setIsManualSyncing(true);
    try {
      const result = await syncService.performSync({ direction: 'bidirectional' });
      setLastSyncResult(result);
      setStatus(syncService.getStatus());
    } catch (error) {
      console.error('Manueller Sync fehlgeschlagen:', error);
    } finally {
      setIsManualSyncing(false);
    }
  };

  const handleForceSync = async () => {
    setIsManualSyncing(true);
    try {
      const result = await syncService.forceFullSync();
      setLastSyncResult(result);
      setStatus(syncService.getStatus());
    } catch (error) {
      console.error('Force-Sync fehlgeschlagen:', error);
    } finally {
      setIsManualSyncing(false);
    }
  };

  const getStatusIcon = () => {
    if (!status.isOnline) {
      return <WifiOff className="h-4 w-4 text-red-500" />;
    }
    if (status.syncInProgress.length > 0) {
      return <RefreshCw className="h-4 w-4 text-blue-500 animate-spin" />;
    }
    if (connectionTest?.supabase) {
      return <Cloud className="h-4 w-4 text-green-500" />;
    }
    return <CloudOff className="h-4 w-4 text-orange-500" />;
  };

  const getStatusText = () => {
    if (!status.isOnline) {
      return 'Offline';
    }
    if (status.syncInProgress.length > 0) {
      return 'Synchronisiert...';
    }
    if (connectionTest?.supabase) {
      return 'Online & Synchronisiert';
    }
    return 'Online (Nur lokal)';
  };

  const getStatusColor = () => {
    if (!status.isOnline) return 'destructive';
    if (status.syncInProgress.length > 0) return 'default';
    if (connectionTest?.supabase) return 'default';
    return 'secondary';
  };

  if (compact) {
    return (
      <div className="flex items-center gap-2">
        {getStatusIcon()}
        <Badge variant={getStatusColor() as any} className="text-xs">
          {getStatusText()}
        </Badge>
      </div>
    );
  }

  return (
    <Card className="w-full">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {getStatusIcon()}
            <CardTitle className="text-lg">Sync-Status</CardTitle>
          </div>
          <Badge variant={getStatusColor() as any}>
            {getStatusText()}
          </Badge>
        </div>
        <CardDescription>
          Synchronisation zwischen Geräten über Supabase
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Grundlegende Informationen */}
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div className="flex items-center gap-2">
            <Wifi className="h-4 w-4" />
            <span>Netzwerk:</span>
            <Badge variant={status.isOnline ? 'default' : 'destructive'}>
              {status.isOnline ? 'Online' : 'Offline'}
            </Badge>
          </div>
          
          <div className="flex items-center gap-2">
            <Smartphone className="h-4 w-4" />
            <span>Gerät:</span>
            <code className="text-xs bg-muted px-1 rounded">
              {status.deviceId.slice(0, 8)}...
            </code>
          </div>
        </div>

        {/* Verbindungsstatus */}
        {connectionTest && (
          <div className="space-y-2">
            <h4 className="text-sm font-medium">Verbindungen:</h4>
            <div className="grid grid-cols-2 gap-2 text-xs">
              <div className="flex items-center gap-2">
                <Database className="h-3 w-3" />
                <span>LocalStorage:</span>
                {connectionTest.localStorage ? (
                  <CheckCircle className="h-3 w-3 text-green-500" />
                ) : (
                  <AlertCircle className="h-3 w-3 text-red-500" />
                )}
              </div>
              <div className="flex items-center gap-2">
                <Cloud className="h-3 w-3" />
                <span>Supabase:</span>
                {connectionTest.supabase ? (
                  <CheckCircle className="h-3 w-3 text-green-500" />
                ) : (
                  <AlertCircle className="h-3 w-3 text-red-500" />
                )}
              </div>
            </div>
          </div>
        )}

        {/* Letzter Sync */}
        {status.lastSync && (
          <div className="text-sm">
            <span className="text-muted-foreground">Letzter Sync: </span>
            <span>
              {formatDistanceToNow(status.lastSync, { 
                addSuffix: true, 
                locale: de 
              })}
            </span>
          </div>
        )}

        {/* Sync in Progress */}
        {status.syncInProgress.length > 0 && (
          <div className="text-sm">
            <span className="text-muted-foreground">Synchronisiert: </span>
            <span className="font-medium">
              {status.syncInProgress.join(', ')}
            </span>
          </div>
        )}

        {/* Letztes Sync-Ergebnis */}
        {showDetails && lastSyncResult && (
          <>
            <Separator />
            <div className="space-y-2">
              <h4 className="text-sm font-medium">Letztes Sync-Ergebnis:</h4>
              <div className="text-xs space-y-1">
                <div className="flex justify-between">
                  <span>Status:</span>
                  <Badge variant={lastSyncResult.success ? 'default' : 'destructive'}>
                    {lastSyncResult.success ? 'Erfolgreich' : 'Fehlgeschlagen'}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span>Datensätze:</span>
                  <span>{lastSyncResult.totalRecords}</span>
                </div>
                <div className="flex justify-between">
                  <span>Dauer:</span>
                  <span>{lastSyncResult.duration}ms</span>
                </div>
              </div>
            </div>
          </>
        )}

        {/* Aktionen */}
        <Separator />
        <div className="flex gap-2">
          <Button
            onClick={handleManualSync}
            disabled={isManualSyncing || !status.isOnline}
            size="sm"
            variant="outline"
          >
            {isManualSyncing ? (
              <RefreshCw className="h-4 w-4 animate-spin mr-2" />
            ) : (
              <RefreshCw className="h-4 w-4 mr-2" />
            )}
            Sync
          </Button>
          
          {showDetails && (
            <Button
              onClick={handleForceSync}
              disabled={isManualSyncing || !status.isOnline}
              size="sm"
              variant="outline"
            >
              Force Sync
            </Button>
          )}
          
          <Button
            onClick={testConnection}
            size="sm"
            variant="ghost"
          >
            Test
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
