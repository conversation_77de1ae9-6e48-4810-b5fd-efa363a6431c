'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { format } from 'date-fns';

import { Button } from '@/components/ui/button';
import { Input } from "@/components/ui/input";
import { DateInput } from "@/components/ui/date-input";
import { Textarea } from '@/components/ui/textarea';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { euroToCents, centsToEuro } from '@/lib/utils';
import { Transaction, Category, createTransaction, updateTransaction, getCategories } from '@/lib/db';

// Schema für die Formularvalidierung
const transactionSchema = z.object({
  description: z.string().min(1, 'Beschreibung ist erforderlich'),
  amount: z.coerce.number().min(0.01, 'Betrag muss größer als 0 sein'),
  date: z.date(),
  categoryId: z.coerce.number().refine(val => val > 0, { message: 'Kategorie ist erforderlich' }),
  type: z.enum(['income', 'expense', 'business_income', 'business_expense', 'investment']),
  notes: z.string().optional(),
});

type TransactionFormValues = z.infer<typeof transactionSchema>;

interface TransactionFormProps {
  onSuccess?: () => void;
  onCancel?: () => void;
  defaultValues?: Partial<Transaction>;
  mode?: 'create' | 'edit';
}

export function TransactionForm({
  onSuccess,
  onCancel,
  defaultValues,
  mode = 'create'
}: TransactionFormProps) {
  const [categories, setCategories] = useState<Category[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const isEditMode = mode === 'edit';

  // Formular initialisieren
  const form = useForm<TransactionFormValues>({
    resolver: zodResolver(transactionSchema),
    defaultValues: {
      description: defaultValues?.description || '',
      // Wenn ein Standardwert vorhanden ist, konvertiere ihn von Cent zu Euro für die Anzeige
      amount: defaultValues?.amount ? centsToEuro(defaultValues.amount) : 0.01, // Mindestbetrag gemäß Validierung
      date: defaultValues?.date ? new Date(defaultValues.date) : new Date(),
      categoryId: defaultValues?.categoryId || undefined, // Undefined statt 0, damit Platzhalter angezeigt wird
      type: defaultValues?.type || 'expense',
      notes: defaultValues?.notes || '',
    },
    // Validierung erst bei Submit durchführen, nicht bei Initialisierung
    mode: 'onSubmit'
  });

  // Typ überwachen für Kategoriefilterung
  const watchType = form.watch("type");

  // Nur im Erstellungsmodus die Kategorie zurücksetzen, wenn der Typ geändert wird
  useEffect(() => {
    if (!isEditMode) {
      // Setze auf 0, damit der Platzhalter angezeigt wird
      // 0 ist ein ungültiger Wert für categoryId (muss > 0 sein laut Schema),
      // wird aber erst bei der Validierung geprüft
      form.setValue("categoryId", 0);
    }
  }, [watchType, form, isEditMode]);

  // Kategorien laden
  useEffect(() => {
    const loadCategories = async () => {
      try {
        const allCategories = await getCategories();
        setCategories(allCategories);
      } catch (error) {
        console.error('Fehler beim Laden der Kategorien:', error);
      }
    };

    loadCategories();
  }, []);

  // Formular absenden
  const onSubmit = async (data: TransactionFormValues) => {
    setIsSubmitting(true);
    try {
      // Konvertiere den Betrag von Euro in Cent (Integer)
      const amountInCents = euroToCents(data.amount);

      const transaction: Transaction = {
        description: data.description,
        // Speichere den Betrag in Cent als Integer
        amount: amountInCents,
        date: format(data.date, 'yyyy-MM-dd'),
        categoryId: data.categoryId,
        type: data.type,
        notes: data.notes,
      };

      if (isEditMode && defaultValues?.id) {
        // Bestehende Transaktion aktualisieren
        transaction.id = defaultValues.id;
        await updateTransaction(transaction);
      } else {
        // Neue Transaktion erstellen
        await createTransaction(transaction);
      }

      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error(`Fehler beim ${isEditMode ? 'Aktualisieren' : 'Speichern'} der Transaktion:`, error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="type"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Typ</FormLabel>
              <Select
                onValueChange={field.onChange}
                defaultValue={field.value}
              >
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Typ auswählen" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="expense">Ausgabe (Privat)</SelectItem>
                  <SelectItem value="income">Einnahme (Privat)</SelectItem>
                  <SelectItem value="business_income">Einnahme (Gewerblich)</SelectItem>
                  <SelectItem value="business_expense">Ausgabe (Gewerblich)</SelectItem>
                  <SelectItem value="investment">Investment</SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Beschreibung</FormLabel>
              <FormControl>
                <Input placeholder="z.B. Einkauf im Supermarkt" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="amount"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Betrag</FormLabel>
              <FormControl>
                <Input
                  name={field.name} // from react-hook-form
                  onBlur={field.onBlur} // from react-hook-form
                  ref={field.ref} // from react-hook-form
                  type="text"
                  inputMode="decimal"
                  pattern="[0-9]*[.,]?[0-9]{0,2}"
                  placeholder="0,00"
                  value={(() => {
                    const v = field.value;
                    
                    // Prüfe auf undefined, null oder leeren String
                    if (v === undefined || v === null || String(v) === "") {
                      return '';
                    }
                    
                    // Konvertiere zu String für konsistente Behandlung (falls es eine Zahl ist)
                    const s = String(v);
                    
                    // Behandle "0", "0.0", "0.00", "0,0", "0,00" etc. als leer für den Placeholder
                    // Normalisiere erst Komma zu Punkt für korrekte Verarbeitung
                    const normalized = s.replace(',', '.');
                    if (parseFloat(normalized) === 0 && normalized.match(/^0(\.0*)?$/)) { 
                      return '';
                    }
                  
                    // Für alle anderen nicht-leeren Werte (Zahlen oder Strings, die Zahlen repräsentieren und nicht Null sind)
                    return s.replace('.', ',');
                  })()}
                  onChange={(e) => {
                    const inputValue = e.target.value;
                    
                    // Allow clearing the field by passing undefined instead of empty string
                    if (inputValue.trim() === "") {
                      field.onChange(undefined);
                      return;
                    }
                    
                    // Normalize comma to dot for internal processing
                    const valueNormalizedComma = inputValue.replace(/,/g, '.');
                    
                    // Filter to allow only digits and at most one decimal separator
                    let finalValue = "";
                    let decimalSeparatorFound = false;
                    
                    for (let i = 0; i < valueNormalizedComma.length; i++) {
                      const char = valueNormalizedComma[i];
                      if (/\d/.test(char)) {
                        finalValue += char;
                      } else if (char === '.' && !decimalSeparatorFound) {
                        finalValue += char;
                        decimalSeparatorFound = true;
                      }
                    }
                    
                    field.onChange(finalValue);
                  }}
                  onKeyDown={(e) => {
                    // Allow: backspace, delete, tab, escape, enter
                    if (['Backspace', 'Delete', 'Tab', 'Escape', 'Enter'].includes(e.key)) {
                      return;
                    }
                    // Allow: Ctrl/Cmd+A, Ctrl/Cmd+C, Ctrl/Cmd+V, Ctrl/Cmd+X
                    if ((e.ctrlKey || e.metaKey) && ['a', 'c', 'v', 'x'].includes(e.key.toLowerCase())) {
                      return;
                    }
                    // Allow: home, end, left, right, up, down
                    if (['Home', 'End', 'ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown'].includes(e.key)) {
                      return;
                    }
                    // Allow: numbers, comma, period
                    if (/[\d.,]/.test(e.key)) {
                      // Prevent multiple decimal separators
                      if ((e.key === '.' || e.key === ',') && (e.currentTarget.value.includes('.') || e.currentTarget.value.includes(','))) {
                        e.preventDefault();
                      }
                      return;
                    }
                    // Prevent all other keys
                    e.preventDefault();
                  }}
                />
              </FormControl>
              <FormDescription>Betrag in Euro. Bitte Komma oder Punkt als Dezimaltrennzeichen verwenden.</FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="date"
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>Datum</FormLabel>
              <div className="flex gap-2">
                <FormControl>
                  <DateInput
                    value={field.value}
                    onChange={(date) => field.onChange(date)}
                  />
                </FormControl>
                <Button 
                  type="button" 
                  variant="outline" 
                  onClick={() => field.onChange(new Date())}
                >
                  Heute
                </Button>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="categoryId"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Kategorie</FormLabel>
              <Select
                onValueChange={(value) => {
                  // Prüfe, ob der Wert ein leerer String ist
                  if (value === '') {
                    field.onChange(undefined); // Setze auf undefined, wenn leer
                  } else {
                    field.onChange(parseInt(value)); // Ansonsten parse als Integer
                  }
                }}
                value={field.value ? field.value.toString() : undefined}
              >
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Kategorie auswählen" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {(() => {
                    // Aktuelle Kategorien basierend auf dem ausgewählten Typ filtern
                    const currentType = watchType;
                    const filteredCategories = categories.filter(category => category.type === currentType);

                    if (filteredCategories.length === 0) {
                      return (
                        <div className="px-2 py-1 text-sm text-gray-500">
                          Keine Kategorien für diesen Typ verfügbar
                        </div>
                      );
                    }

                    return filteredCategories
                      .sort((a, b) => a.name.localeCompare(b.name, 'de'))
                      .map((category) => (
                      <SelectItem
                        key={category.id}
                        value={category.id?.toString() || ''}
                      >
                        <div className="flex items-center">
                          <div
                            className="w-3 h-3 rounded-full mr-2"
                            style={{ backgroundColor: category.color }}
                          />
                          {category.name}
                        </div>
                      </SelectItem>
                    ));
                  })()}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="notes"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Notizen</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Zusätzliche Informationen (optional)"
                  className="resize-none"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="flex justify-end space-x-2">
          {onCancel && (
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isSubmitting}
            >
              Abbrechen
            </Button>
          )}
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting
              ? `Wird ${isEditMode ? 'aktualisiert' : 'gespeichert'}...`
              : isEditMode ? 'Aktualisieren' : 'Speichern'}
          </Button>
        </div>
      </form>
    </Form>
  );
}
