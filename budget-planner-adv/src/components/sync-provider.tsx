"use client";

import React, { createContext, useContext, useEffect, useState } from 'react';
import { syncService, SyncResult } from '@/lib/supabase';

interface SyncContextType {
  isOnline: boolean;
  lastSync: Date | null;
  autoSyncActive: boolean;
  deviceId: string;
  syncInProgress: string[];
  lastSyncResult: SyncResult | null;
  performSync: () => Promise<void>;
  forceSync: () => Promise<void>;
  pushAllData: () => Promise<void>;
}

const SyncContext = createContext<SyncContextType | null>(null);

export function useSyncContext() {
  const context = useContext(SyncContext);
  if (!context) {
    throw new Error('useSyncContext muss innerhalb eines SyncProvider verwendet werden');
  }
  return context;
}

interface SyncProviderProps {
  children: React.ReactNode;
}

export function SyncProvider({ children }: SyncProviderProps) {
  const [status, setStatus] = useState(syncService.getStatus());
  const [lastSyncResult, setLastSyncResult] = useState<SyncResult | null>(null);

  // Status regelmäßig aktualisieren
  useEffect(() => {
    const interval = setInterval(() => {
      setStatus(syncService.getStatus());
    }, 2000);

    return () => clearInterval(interval);
  }, []);

  // Sync-Service beim Mount starten
  useEffect(() => {
    const initializeSync = async () => {
      try {
        await syncService.start();
        console.log('Sync-Service erfolgreich gestartet');
      } catch (error) {
        console.error('Fehler beim Starten des Sync-Service:', error);
      }
    };

    initializeSync();

    // Cleanup beim Unmount
    return () => {
      syncService.stop();
    };
  }, []);

  const performSync = async () => {
    try {
      const result = await syncService.performSync({ direction: 'bidirectional' });
      setLastSyncResult(result);
      setStatus(syncService.getStatus());
    } catch (error) {
      console.error('Sync fehlgeschlagen:', error);
      throw error;
    }
  };

  const forceSync = async () => {
    try {
      const result = await syncService.forceFullSync();
      setLastSyncResult(result);
      setStatus(syncService.getStatus());
    } catch (error) {
      console.error('Force-Sync fehlgeschlagen:', error);
      throw error;
    }
  };

  const pushAllData = async () => {
    try {
      const result = await syncService.pushAllData();
      setLastSyncResult(result);
      setStatus(syncService.getStatus());
    } catch (error) {
      console.error('Push-All fehlgeschlagen:', error);
      throw error;
    }
  };

  const contextValue: SyncContextType = {
    isOnline: status.isOnline,
    lastSync: status.lastSync,
    autoSyncActive: status.autoSyncActive,
    deviceId: status.deviceId,
    syncInProgress: status.syncInProgress,
    lastSyncResult,
    performSync,
    forceSync,
    pushAllData,
  };

  return (
    <SyncContext.Provider value={contextValue}>
      {children}
    </SyncContext.Provider>
  );
}
