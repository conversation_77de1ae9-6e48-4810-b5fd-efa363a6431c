"use client";

import { <PERSON>, CardContent, Card<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { PlusCircle, Edit } from "lucide-react";
import Link from "next/link";
import { useEffect, useState } from "react";
import { TransactionDialog } from "@/components/transactions/transaction-dialog";
import { ExpenseCategoryChart } from "@/components/dashboard/expense-category-chart";
import { IncomeExpenseChart } from "@/components/dashboard/income-expense-chart";
import { AccountBalanceDialog } from "@/components/dashboard/account-balance-dialog";
import { SyncStatus } from "@/components/sync-status";
import { getTransactions, getSavingsGoals, getInvestments, getCategories, getAccountBalancesByMonth } from "@/lib/db";
import { formatCurrency } from "@/lib/utils";
import { useSyncContext } from "@/components/sync-provider";

// Typen für die Dashboard-Daten
interface DashboardTransaction {
  id?: number;
  description: string;
  amount: number;
  date: string;
  categoryId: number;
  categoryName: string;
  type: 'income' | 'expense' | 'business_income' | 'business_expense' | 'investment';
  notes?: string;
}

interface DashboardData {
  income: number;
  expense: number;
  savings: number;
  investmentValue: number;
  recentTransactions: DashboardTransaction[];
  privateAccountBalance: number;
  businessAccountBalance: number;
}

export default function Home() {
  const [isClient, setIsClient] = useState(false);
  const [refreshKey, setRefreshKey] = useState(0);
  const syncContext = useSyncContext();
  const [dashboardData, setDashboardData] = useState<DashboardData>({
    income: 0,
    expense: 0,
    savings: 0,
    investmentValue: 0,
    recentTransactions: [],
    privateAccountBalance: 0,
    businessAccountBalance: 0
  });
  const [yearData, setYearData] = useState<DashboardData>({
    income: 0,
    expense: 0,
    savings: 0,
    investmentValue: 0,
    recentTransactions: [],
    privateAccountBalance: 0,
    businessAccountBalance: 0
  });
  const [allTimeData, setAllTimeData] = useState<DashboardData>({
    income: 0,
    expense: 0,
    savings: 0,
    investmentValue: 0,
    recentTransactions: [],
    privateAccountBalance: 0,
    businessAccountBalance: 0
  });
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    setIsClient(true);

    // Flag, um zu prüfen, ob die Komponente noch gemountet ist
    let isMounted = true;

    async function loadDashboardData() {
      try {
        if (isMounted) setIsLoading(true);

        // Aktuellen Monat bestimmen
        const now = new Date();
        const year = now.getFullYear();
        const month = now.getMonth() + 1;

        // Erster und letzter Tag des aktuellen Monats
        const startDate = `${year}-${month.toString().padStart(2, '0')}-01`;
        const lastDay = new Date(year, month, 0).getDate();
        const endDate = `${year}-${month.toString().padStart(2, '0')}-${lastDay}`;

        // Lade alle Transaktionen
        const allTransactions = await getTransactions();
        const categories = await getCategories();

        // Filtere Transaktionen für den aktuellen Monat
        const transactions = allTransactions.filter(t => {
          const transactionDate = new Date(t.date);
          return transactionDate >= new Date(startDate) && transactionDate <= new Date(endDate);
        });

        // Berechne Einnahmen und Ausgaben für den aktuellen Monat
        const income = transactions
          .filter(t => t.type === 'income' || t.type === 'business_income')
          .reduce((sum, t) => sum + t.amount, 0);

        // Berücksichtige auch Investments als Ausgaben
        const expense = transactions
          .filter(t => t.type === 'expense' || t.type === 'business_expense' || t.type === 'investment')
          .reduce((sum, t) => sum + t.amount, 0);

        // Lade Sparziele
        const savingsGoals = await getSavingsGoals();
        const totalSavings = savingsGoals.reduce((sum, goal) => sum + goal.currentAmount, 0);

        // Lade Investments
        const investments = await getInvestments();
        const totalInvestmentValue = investments.reduce((sum, inv) => sum + inv.currentValue, 0);

        // Lade Kontostände für den aktuellen Monat
        const accountBalances = await getAccountBalancesByMonth(month, year);
        const privateBalance = accountBalances.find(b => b.type === 'private')?.balance || 0;
        const businessBalance = accountBalances.find(b => b.type === 'business')?.balance || 0;

        // Sortiere nach Datum und nimm die neuesten 5
        const recentTransactions = allTransactions
          .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
          .slice(0, 5)
          .map(transaction => {
            // Füge Kategorienamen hinzu
            const category = categories.find(c => c.id === transaction.categoryId);
            return {
              ...transaction,
              categoryName: category?.name || 'Unbekannt'
            };
          });

        // Setze die Daten für den aktuellen Monat, wenn die Komponente noch gemountet ist
        if (isMounted) {
          setDashboardData({
            income,
            expense,
            savings: totalSavings,
            investmentValue: totalInvestmentValue,
            recentTransactions,
            privateAccountBalance: privateBalance,
            businessAccountBalance: businessBalance
          });
        }

        // Berechne Daten für das aktuelle Jahr
        const yearStartDate = `${year}-01-01`;
        const yearEndDate = `${year}-12-31`;

        const yearTransactions = await getTransactions({
          startDate: yearStartDate,
          endDate: yearEndDate
        });

        const yearIncome = yearTransactions
          .filter(t => t.type === 'income' || t.type === 'business_income')
          .reduce((sum, t) => sum + t.amount, 0);

        const yearExpense = yearTransactions
          .filter(t => t.type === 'expense' || t.type === 'business_expense' || t.type === 'investment')
          .reduce((sum, t) => sum + t.amount, 0);

        // Setze die Daten für das aktuelle Jahr, wenn die Komponente noch gemountet ist
        if (isMounted) {
          setYearData({
            income: yearIncome,
            expense: yearExpense,
            savings: totalSavings,
            investmentValue: totalInvestmentValue,
            recentTransactions: [],
            privateAccountBalance: privateBalance,
            businessAccountBalance: businessBalance
          });
        }

        // Berechne Daten für alle Zeiten
        const allTimeIncome = allTransactions
          .filter(t => t.type === 'income' || t.type === 'business_income')
          .reduce((sum, t) => sum + t.amount, 0);

        const allTimeExpense = allTransactions
          .filter(t => t.type === 'expense' || t.type === 'business_expense' || t.type === 'investment')
          .reduce((sum, t) => sum + t.amount, 0);

        // Setze die Daten für alle Zeiten, wenn die Komponente noch gemountet ist
        if (isMounted) {
          setAllTimeData({
            income: allTimeIncome,
            expense: allTimeExpense,
            savings: totalSavings,
            investmentValue: totalInvestmentValue,
            recentTransactions: [],
            privateAccountBalance: privateBalance,
            businessAccountBalance: businessBalance
          });
        }
      } catch (error) {
        console.error("Fehler beim Laden der Dashboard-Daten:", error);
      } finally {
        // Setze isLoading nur zurück, wenn die Komponente noch gemountet ist
        if (isMounted) {
          setIsLoading(false);
        }
      }
    }

    loadDashboardData();
    console.log('Dashboard neu geladen', refreshKey);

    // Cleanup-Funktion, die beim Unmounten der Komponente aufgerufen wird
    return () => {
      isMounted = false;
    };
  }, [refreshKey]);

  const handleTransactionSuccess = () => {
    // Erhöhe den refreshKey, um die Komponente neu zu rendern
    setRefreshKey(prev => prev + 1);
  };

  if (!isClient || isLoading) {
    return <div className="flex items-center justify-center h-screen">
      <p className="text-slate-500">Dashboard wird geladen...</p>
    </div>;
  }

  const content = (
    <div className="flex flex-col gap-6 w-full">
      <div className="flex items-center justify-between w-full">
        <h1 className="text-3xl font-bold">Dashboard</h1>
        <div className="flex items-center gap-4">
          <SyncStatus compact />
          <TransactionDialog
            trigger={
              <Button className="bg-black text-white hover:bg-gray-800">
                <PlusCircle className="mr-2 h-4 w-4" />
                Neue Transaktion
              </Button>
            }
            onSuccess={handleTransactionSuccess}
          />
        </div>
      </div>

      <Tabs defaultValue="month" className="w-full">
        <TabsList>
          <TabsTrigger value="month">Monat</TabsTrigger>
          <TabsTrigger value="year">Jahr</TabsTrigger>
          <TabsTrigger value="all">Gesamt</TabsTrigger>
        </TabsList>
        <TabsContent value="month" className="space-y-6">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Einnahmen</CardTitle>
                <CardDescription>Aktueller Monat</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-emerald-500">{formatCurrency(dashboardData.income, true)}</div>
                <p className="text-xs text-slate-500">Gesamteinnahmen im aktuellen Monat</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Ausgaben</CardTitle>
                <CardDescription>Aktueller Monat</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-rose-500">{formatCurrency(dashboardData.expense, true)}</div>
                <p className="text-xs text-slate-500">Gesamtausgaben im aktuellen Monat</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Verfügbarer Betrag</CardTitle>
                <CardDescription>Aktueller Monat</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-blue-500">
                  {formatCurrency(dashboardData.income - dashboardData.expense + dashboardData.privateAccountBalance + dashboardData.businessAccountBalance, true)}
                </div>
                <p className="text-xs text-slate-500">Differenz + Kontostand</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2 flex flex-row items-center justify-between space-y-0">
                <div>
                  <CardTitle className="text-sm font-medium">Kontostand</CardTitle>
                  <CardDescription>Aktueller Stand</CardDescription>
                </div>
                <AccountBalanceDialog
                  trigger={
                    <Button variant="ghost" size="icon" className="h-8 w-8">
                      <Edit className="h-4 w-4" />
                    </Button>
                  }
                  month={new Date().getMonth() + 1}
                  year={new Date().getFullYear()}
                  onSuccess={handleTransactionSuccess}
                />
              </CardHeader>
              <CardContent>
                <div className="space-y-1">
                  <div className="flex justify-between items-center">
                    <span className="text-xs text-slate-500">Privat:</span>
                    <span className="text-sm font-medium">{formatCurrency(dashboardData.privateAccountBalance, true)}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-xs text-slate-500">Geschäft:</span>
                    <span className="text-sm font-medium">{formatCurrency(dashboardData.businessAccountBalance, true)}</span>
                  </div>
                  <div className="flex justify-between items-center pt-1 border-t">
                    <span className="text-xs font-medium">Gesamt:</span>
                    <span className="text-sm font-bold">{formatCurrency(dashboardData.privateAccountBalance + dashboardData.businessAccountBalance, true)}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Investments</CardTitle>
                <CardDescription>Aktueller Wert</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-amber-500">{formatCurrency(dashboardData.investmentValue, true)}</div>
                <p className="text-xs text-slate-500">Gesamtwert aller Investments</p>
              </CardContent>
            </Card>
          </div>

          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Ausgaben nach Kategorie</CardTitle>
                <CardDescription>Aktueller Monat</CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <ExpenseCategoryChart />
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>Einnahmen/Ausgaben</CardTitle>
                <CardDescription>Letzte 6 Monate</CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <IncomeExpenseChart />
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Letzte Transaktionen</CardTitle>
              <CardDescription>Die letzten {dashboardData.recentTransactions.length} Transaktionen</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {dashboardData.recentTransactions.length > 0 ? (
                  dashboardData.recentTransactions.map((transaction, index) => {
                    // Bestimme die Textfarbe basierend auf dem Transaktionstyp
                    const isExpense = transaction.type === 'expense' || transaction.type === 'business_expense';
                    const isIncome = transaction.type === 'income' || transaction.type === 'business_income';
                    const isInvestment = transaction.type === 'investment';

                    let textColorClass = '';
                    let amountPrefix = '';

                    if (isExpense) {
                      textColorClass = 'text-rose-500';
                      amountPrefix = '-';
                    } else if (isIncome) {
                      textColorClass = 'text-emerald-500';
                      amountPrefix = '+';
                    } else if (isInvestment) {
                      textColorClass = 'text-amber-500';
                      amountPrefix = '+';
                    }

                    // Formatiere das Datum
                    const date = new Date(transaction.date);
                    const formattedDate = date.toLocaleDateString('de-DE');

                    return (
                      <div
                        key={transaction.id}
                        className={`flex items-center justify-between ${index < dashboardData.recentTransactions.length - 1 ? 'border-b pb-2' : ''}`}
                      >
                        <div>
                          <p className="font-medium">{transaction.description}</p>
                          <p className="text-sm text-slate-500">{transaction.categoryName || transaction.type}</p>
                        </div>
                        <div className="text-right">
                          <p className={`font-medium ${textColorClass}`}>
                            {amountPrefix}{formatCurrency(transaction.amount, true)}
                          </p>
                          <p className="text-sm text-slate-500">{formattedDate}</p>
                        </div>
                      </div>
                    );
                  })
                ) : (
                  <div className="text-center py-4">
                    <p className="text-slate-500">Keine Transaktionen vorhanden</p>
                  </div>
                )}
              </div>
              <div className="mt-4">
                <Link href="/transactions">
                  <Button variant="outline" className="w-full">Alle Transaktionen anzeigen</Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="year" className="space-y-6">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Einnahmen</CardTitle>
                <CardDescription>Aktuelles Jahr</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-emerald-500">{formatCurrency(yearData.income, true)}</div>
                <p className="text-xs text-slate-500">Gesamteinnahmen im aktuellen Jahr</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Ausgaben</CardTitle>
                <CardDescription>Aktuelles Jahr</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-rose-500">{formatCurrency(yearData.expense, true)}</div>
                <p className="text-xs text-slate-500">Gesamtausgaben im aktuellen Jahr</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Verfügbarer Betrag</CardTitle>
                <CardDescription>Aktuelles Jahr</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-blue-500">
                  {formatCurrency(yearData.income - yearData.expense + yearData.privateAccountBalance + yearData.businessAccountBalance, true)}
                </div>
                <p className="text-xs text-slate-500">Differenz + Kontostand</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Kontostand</CardTitle>
                <CardDescription>Aktueller Stand</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-1">
                  <div className="flex justify-between items-center">
                    <span className="text-xs text-slate-500">Privat:</span>
                    <span className="text-sm font-medium">{formatCurrency(yearData.privateAccountBalance, true)}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-xs text-slate-500">Geschäft:</span>
                    <span className="text-sm font-medium">{formatCurrency(yearData.businessAccountBalance, true)}</span>
                  </div>
                  <div className="flex justify-between items-center pt-1 border-t">
                    <span className="text-xs font-medium">Gesamt:</span>
                    <span className="text-sm font-bold">{formatCurrency(yearData.privateAccountBalance + yearData.businessAccountBalance, true)}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Investments</CardTitle>
                <CardDescription>Aktueller Wert</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-amber-500">{formatCurrency(yearData.investmentValue, true)}</div>
                <p className="text-xs text-slate-500">Gesamtwert aller Investments</p>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Jahresübersicht</CardTitle>
              <CardDescription>Einnahmen und Ausgaben im aktuellen Jahr</CardDescription>
            </CardHeader>
            <CardContent className="h-80">
              <div className="flex items-center justify-center h-full">
                <p className="text-slate-500">Jahresdiagramm wird hier angezeigt</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="all" className="space-y-6">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Einnahmen</CardTitle>
                <CardDescription>Alle Zeiten</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-emerald-500">{formatCurrency(allTimeData.income, true)}</div>
                <p className="text-xs text-slate-500">Gesamteinnahmen aller Zeiten</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Ausgaben</CardTitle>
                <CardDescription>Alle Zeiten</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-rose-500">{formatCurrency(allTimeData.expense, true)}</div>
                <p className="text-xs text-slate-500">Gesamtausgaben aller Zeiten</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Verfügbarer Betrag</CardTitle>
                <CardDescription>Alle Zeiten</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-blue-500">
                  {formatCurrency(allTimeData.income - allTimeData.expense + allTimeData.privateAccountBalance + allTimeData.businessAccountBalance, true)}
                </div>
                <p className="text-xs text-slate-500">Differenz + Kontostand</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Kontostand</CardTitle>
                <CardDescription>Aktueller Stand</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-1">
                  <div className="flex justify-between items-center">
                    <span className="text-xs text-slate-500">Privat:</span>
                    <span className="text-sm font-medium">{formatCurrency(allTimeData.privateAccountBalance, true)}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-xs text-slate-500">Geschäft:</span>
                    <span className="text-sm font-medium">{formatCurrency(allTimeData.businessAccountBalance, true)}</span>
                  </div>
                  <div className="flex justify-between items-center pt-1 border-t">
                    <span className="text-xs font-medium">Gesamt:</span>
                    <span className="text-sm font-bold">{formatCurrency(allTimeData.privateAccountBalance + allTimeData.businessAccountBalance, true)}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Investments</CardTitle>
                <CardDescription>Aktueller Wert</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-amber-500">{formatCurrency(allTimeData.investmentValue, true)}</div>
                <p className="text-xs text-slate-500">Gesamtwert aller Investments</p>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Gesamtübersicht</CardTitle>
              <CardDescription>Einnahmen und Ausgaben aller Zeiten</CardDescription>
            </CardHeader>
            <CardContent className="h-80">
              <div className="flex items-center justify-center h-full">
                <p className="text-slate-500">Gesamtdiagramm wird hier angezeigt</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );

  return content;
}
