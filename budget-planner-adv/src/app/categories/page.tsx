"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { PlusCircle, Edit, Trash2 } from "lucide-react";
import { getCategories, deleteCategory } from "@/lib/db";
import type { Category } from "@/lib/db";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

// Import der CategoryDialog-Komponente
import { CategoryDialog } from "@/components/categories/category-dialog";

export default function CategoriesPage() {
  const [isClient, setIsClient] = useState(false);
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [deleteId, setDeleteId] = useState<number | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);

  useEffect(() => {
    setIsClient(true);
    loadCategories();
  }, []);

  async function loadCategories() {
    try {
      setLoading(true);
      const categoriesData = await getCategories();
      setCategories(categoriesData);
    } catch (error) {
      console.error("Fehler beim Laden der Kategorien:", error);
    } finally {
      setLoading(false);
    }
  }

  async function handleDeleteCategory() {
    if (deleteId === null) return;

    try {
      await deleteCategory(deleteId);
      setCategories(categories.filter(c => c.id !== deleteId));
      setDeleteDialogOpen(false);
      setDeleteId(null);
    } catch (error) {
      console.error("Fehler beim Löschen der Kategorie:", error);
    }
  }

  function getCategoryTypeLabel(type: string): string {
    switch (type) {
      case 'income':
        return 'Einnahme (Privat)';
      case 'expense':
        return 'Ausgabe (Privat)';
      case 'investment':
        return 'Investment';
      case 'business_income':
        return 'Einnahme (Gewerblich)';
      case 'business_expense':
        return 'Ausgabe (Gewerblich)';
      default:
        return type;
    }
  }

  function getCategoryTypeColor(type: string): string {
    switch (type) {
      case 'income':
        return 'text-emerald-500';
      case 'expense':
        return 'text-rose-500';
      case 'investment':
        return 'text-amber-500';
      case 'business_income':
        return 'text-green-600';
      case 'business_expense':
        return 'text-red-600';
      default:
        return 'text-gray-500';
    }
  }

  function getCategoryGroup(type: string): string {
    switch (type) {
      case 'income':
      case 'expense':
        return 'Privat';
      case 'business_income':
      case 'business_expense':
        return 'Gewerblich';
      case 'investment':
        return 'Investitionen';
      default:
        return 'Sonstige';
    }
  }

  if (!isClient) {
    return <div>Loading...</div>;
  }

  const content = (
    <div className="flex flex-col gap-6 w-full">
      <div className="flex items-center justify-between w-full mb-6">
        <h1 className="text-3xl font-bold">Kategorien</h1>
        <div className="flex gap-2">
          <CategoryDialog
            onSuccess={loadCategories}
            trigger={
              <Button className="bg-black text-white hover:bg-gray-800">
                <PlusCircle className="mr-2 h-4 w-4" />
                Neue Kategorie
              </Button>
            }
          />
        </div>
      </div>

      {loading ? (
        <div className="flex justify-center p-8">
          <p>Lade Kategorien...</p>
        </div>
      ) : categories.length === 0 ? (
        <div className="bg-white p-6 rounded-lg shadow text-center">
          <p className="text-gray-500 py-10">
            Keine Kategorien gefunden. Erstellen Sie Ihre erste Kategorie.
          </p>
        </div>
      ) : (
        <div className="space-y-8">
          {/* Gruppiere Kategorien nach Typ */}
          {['Privat', 'Gewerblich', 'Investitionen'].map((group) => {
            const groupCategories = categories.filter(
              (category) => getCategoryGroup(category.type) === group
            );

            if (groupCategories.length === 0) return null;

            return (
              <div key={group} className="space-y-4">
                <h2 className="text-xl font-bold border-b pb-2">{group}</h2>
                <div className="grid gap-3 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5">
                  {groupCategories
                    .sort((a, b) => a.name.localeCompare(b.name, 'de'))
                    .map((category) => (
                    <Card
                      key={category.id}
                      className="overflow-hidden border"
                    >
                      <div
                        className="h-2"
                        style={{ backgroundColor: category.color }}
                      ></div>
                      <CardContent className="p-3">
                        <div className="flex items-center justify-between">
                          <div className="flex-1 min-w-0">
                            <h3 className="font-bold text-md truncate mb-1">{category.name}</h3>
                            <p className={`text-xs ${getCategoryTypeColor(category.type)}`}>
                              {getCategoryTypeLabel(category.type)}
                            </p>
                          </div>
                          <div className="flex gap-1 ml-2 flex-shrink-0">
                            <CategoryDialog
                              category={category}
                              mode="edit"
                              onSuccess={loadCategories}
                              trigger={
                                <Button variant="ghost" size="icon" className="h-7 w-7 text-gray-500 hover:text-gray-700">
                                  <Edit className="h-3.5 w-3.5" />
                                </Button>
                              }
                            />
                            <AlertDialog open={deleteDialogOpen && deleteId === category.id} onOpenChange={setDeleteDialogOpen}>
                              <AlertDialogTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  className="h-7 w-7 text-gray-500 hover:text-gray-700"
                                  onClick={() => setDeleteId(category.id || null)}
                                >
                                  <Trash2 className="h-3.5 w-3.5" />
                                </Button>
                              </AlertDialogTrigger>
                              <AlertDialogContent>
                                <AlertDialogHeader>
                                  <AlertDialogTitle>Kategorie löschen</AlertDialogTitle>
                                  <AlertDialogDescription>
                                    Möchten Sie diese Kategorie wirklich löschen? Diese Aktion kann nicht rückgängig gemacht werden.
                                    Alle Transaktionen, die dieser Kategorie zugeordnet sind, werden nicht gelöscht, aber verlieren ihre Kategorie.
                                  </AlertDialogDescription>
                                </AlertDialogHeader>
                                <AlertDialogFooter>
                                  <AlertDialogCancel onClick={() => setDeleteId(null)}>Abbrechen</AlertDialogCancel>
                                  <AlertDialogAction onClick={handleDeleteCategory} className="bg-rose-500 hover:bg-rose-600">
                                    Löschen
                                  </AlertDialogAction>
                                </AlertDialogFooter>
                              </AlertDialogContent>
                            </AlertDialog>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );

  return content;
}
