"use client";

import { useState, useEffect } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card";
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { getSettings, updateSettings } from "@/lib/settings-db";
import { SyncStatus } from "@/components/sync-status";
import { useSyncContext } from "@/components/sync-provider";
import { syncService } from "@/lib/supabase";

// Schema für die Formularvalidierung
const settingsFormSchema = z.object({
  currency: z.string().min(1, {
    message: "Bitte wähle eine Währung aus.",
  }),
  dateFormat: z.string().min(1, {
    message: "Bitte wähle ein Datumsformat aus.",
  }),
  theme: z.string().min(1, {
    message: "Bitte wähle ein Theme aus.",
  }),
  fontSize: z.string().min(1, {
    message: "Bitte wähle eine Schriftgröße aus.",
  }),
  defaultView: z.string().min(1, {
    message: "Bitte wähle eine Standardansicht aus.",
  }),
});

type SettingsFormValues = z.infer<typeof settingsFormSchema>;

export default function SettingsPage() {
  const [isClient, setIsClient] = useState(false);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("general");
  const [saveSuccess, setSaveSuccess] = useState(false);
  const [saveError, setSaveError] = useState<string | null>(null);
  const [isTauriApp, setIsTauriApp] = useState(false);
  const syncContext = useSyncContext();

  // Formular initialisieren
  const form = useForm<SettingsFormValues>({
    resolver: zodResolver(settingsFormSchema),
    defaultValues: {
      currency: "EUR",
      dateFormat: "DD.MM.YYYY",
      theme: "system",
      fontSize: "medium",
      defaultView: "dashboard",
    },
  });

  useEffect(() => {
    setIsClient(true);
    
    // Forciere isTauriApp auf true für die Mac-App
    // In der Tauri-Umgebung können wir die isTauri-Funktion verwenden
    // Für die Mac-App-Version setzen wir es immer auf true
    setIsTauriApp(true);
    
    loadSettings();
  }, []);
  
  // Füge loadSettings als Dependency hinzu, um den Lint-Fehler zu beheben
  // eslint-disable-next-line react-hooks/exhaustive-deps

  async function loadSettings() {
    try {
      setLoading(true);
      const settings = await getSettings();

      if (settings) {
        form.reset({
          currency: settings.currency,
          dateFormat: settings.dateFormat,
          theme: settings.theme,
          fontSize: settings.fontSize,
          defaultView: settings.defaultView,
        });
      }
    } catch (error) {
      console.error("Fehler beim Laden der Einstellungen:", error);
    } finally {
      setLoading(false);
    }
  }

  async function onSubmit(data: SettingsFormValues) {
    try {
      // Zurücksetzen von Erfolgs- und Fehlermeldungen
      setSaveSuccess(false);
      setSaveError(null);

      await updateSettings(data);
      setSaveSuccess(true);

      // Erfolgsmeldung nach 3 Sekunden ausblenden
      setTimeout(() => {
        setSaveSuccess(false);
      }, 3000);
    } catch (error) {
      console.error("Fehler beim Speichern der Einstellungen:", error);
      setSaveError("Fehler beim Speichern der Einstellungen. Bitte versuche es erneut.");

      // Fehlermeldung nach 5 Sekunden ausblenden
      setTimeout(() => {
        setSaveError(null);
      }, 5000);
    }
  }

  if (!isClient) {
    return <div>Loading...</div>;
  }

  return (
    <div className="flex flex-col gap-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Einstellungen</h1>
        {!isTauriApp && (
          <Button onClick={() => window.open('/storage-manager.html', '_blank')}>
            Backup & Wiederherstellung
          </Button>
        )}
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)}>
          <Tabs defaultValue="general" value={activeTab} onValueChange={setActiveTab}>
            <TabsList>
              <TabsTrigger value="general">Persönlich</TabsTrigger>
              <TabsTrigger value="sync">Synchronisation</TabsTrigger>
              {!isTauriApp && (
                <TabsTrigger value="advanced">Datenbank</TabsTrigger>
              )}
            </TabsList>

            <TabsContent value="general" className="space-y-4 mt-4">
              <Card>
                <CardHeader>
                  <CardTitle>Persönliche Einstellungen</CardTitle>
                  <CardDescription>
                    Grundlegende Einstellungen für die Anwendung
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <FormField
                      control={form.control}
                      name="currency"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Währung</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Währung auswählen" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="EUR">Euro (€)</SelectItem>
                              <SelectItem value="USD">US-Dollar ($)</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormDescription>
                            Die Währung, die für alle Beträge verwendet wird.
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="dateFormat"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Datumsformat</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Datumsformat auswählen" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="DD.MM.YYYY">DD.MM.YYYY</SelectItem>
                              <SelectItem value="MM/DD/YYYY">MM/DD/YYYY</SelectItem>
                              <SelectItem value="YYYY-MM-DD">YYYY-MM-DD</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormDescription>
                            Das Format, in dem Datumsangaben angezeigt werden.
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="theme"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Theme</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Theme auswählen" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="light">Hell</SelectItem>
                              <SelectItem value="dark">Dunkel</SelectItem>
                              <SelectItem value="system">Systemeinstellung</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormDescription>
                            Das Farbschema der Anwendung.
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="fontSize"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Schriftgröße</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Schriftgröße auswählen" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="small">Klein</SelectItem>
                              <SelectItem value="medium">Mittel</SelectItem>
                              <SelectItem value="large">Groß</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormDescription>
                            Die Größe der Schrift in der Anwendung.
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="defaultView"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Standardansicht</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Standardansicht auswählen" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="dashboard">Dashboard</SelectItem>
                              <SelectItem value="transactions">Transaktionen</SelectItem>
                              <SelectItem value="savings">Sparziele</SelectItem>
                              <SelectItem value="investments">Investments</SelectItem>
                              <SelectItem value="categories">Kategorien</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormDescription>
                            Die Ansicht, die beim Start der Anwendung angezeigt wird.
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="sync" className="space-y-4 mt-4">
              <Card>
                <CardHeader>
                  <CardTitle>Synchronisation</CardTitle>
                  <CardDescription>
                    Verwalte die Synchronisation zwischen deinen Geräten über Supabase
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <SyncStatus showDetails />

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => syncContext.performSync()}
                      disabled={!syncContext.isOnline}
                    >
                      Manueller Sync
                    </Button>

                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => syncContext.forceSync()}
                      disabled={!syncContext.isOnline}
                    >
                      Vollständiger Sync
                    </Button>

                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => syncContext.pushAllData()}
                      disabled={!syncContext.isOnline}
                    >
                      Alle Daten hochladen
                    </Button>

                    <Button
                      type="button"
                      variant="destructive"
                      onClick={() => {
                        if (confirm('Möchtest du wirklich alle lokalen Daten mit den Remote-Daten überschreiben?')) {
                          syncContext.forceSync();
                        }
                      }}
                      disabled={!syncContext.isOnline}
                    >
                      Remote-Daten herunterladen
                    </Button>
                  </div>

                  <div className="space-y-2">
                    <h4 className="text-sm font-medium">Sync-Informationen:</h4>
                    <div className="text-xs space-y-1 bg-muted p-3 rounded">
                      <div>Gerät-ID: <code>{syncContext.deviceId}</code></div>
                      <div>Status: {syncContext.isOnline ? 'Online' : 'Offline'}</div>
                      <div>Auto-Sync: {syncContext.autoSyncActive ? 'Aktiv' : 'Inaktiv'}</div>
                      {syncContext.lastSync && (
                        <div>Letzter Sync: {syncContext.lastSync.toLocaleString('de-DE')}</div>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {!isTauriApp && (
              <TabsContent value="advanced" className="space-y-4 mt-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Datenbank-Verwaltung</CardTitle>
                    <CardDescription>
                      Verwalte deine Datenbank und Daten
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="grid grid-cols-1 gap-6">
                      <div className="flex flex-col gap-4">
                        <p className="text-sm">
                          Hier kannst du deine Datenbank verwalten, Backups erstellen und wiederherstellen,
                          sowie alle Daten einsehen und bearbeiten.
                        </p>

                        <div className="flex flex-col gap-2">
                          <h3 className="text-lg font-medium">Datenbank-Funktionen</h3>
                          <p className="text-sm text-slate-500">
                            Verwalte deine Daten und Einstellungen.
                          </p>

                          <div className="flex flex-wrap gap-2 mt-2">
                            <Button
                              type="button"
                              variant="outline"
                              onClick={() => window.open('/storage-manager.html', '_blank')}
                            >
                              Datenbank-Verwaltung
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            )}
          </Tabs>

          <div className="flex justify-end mt-6">
            {saveSuccess && (
              <p className="text-emerald-500 mr-4 self-center">
                Einstellungen erfolgreich gespeichert!
              </p>
            )}
            {saveError && (
              <p className="text-red-500 mr-4 self-center">
                {saveError}
              </p>
            )}
            <Button type="submit" disabled={loading}>
              Einstellungen speichern
            </Button>
          </div>
        </form>
      </Form>

      {/* Backup-Dialog wurde für die Mac-App entfernt */}
    </div>
  );
}
